import { http, HttpResponse } from 'msw'
import { assetsApiUrl } from '../mock-utils'

export default [
  http.get(assetsApiUrl('foquz/api/p/answer-variables'), () => {
    // Generic variables response
    const variables = {
      'fio': '<PERSON><PERSON><PERSON><PERSON> Иванов',
      'order': null,
      'codes': {
        123: 'PROMO-123',
        456: 'DISCOUNT-456',
      },
      'scoresInterpretationRanges': [
        {
          id: 1,
          min: 0,
          max: 3,
          result: 'Низкий уровень',
          description: 'Требуется улучшение',
        },
        {
          id: 2,
          min: 4,
          max: 7,
          result: 'Средний уровень',
          description: 'Хо<PERSON>ошо, но можно лучше',
        },
        {
          id: 3,
          min: 8,
          max: 10,
          result: 'Высокий уровень',
          description: 'Отличный результат',
        },
      ],
      'FILIAL.param1': 'Значение параметра 1',
      'FILIAL.param2': 'Значение параметра 2',
      'FILIAL.param3': 'Значение параметра 3',
      'ANSWER.1': 'Ответ на первый вопрос',
      'ANSWER.2': 'Ответ на второй вопрос',
      'ANSWER.3': 'Ответ на третий вопрос',
      'URL.test': 'test-value',
      'URL.param1': 'param1-value',
      'URL.param2': 'param2-value',
    }

    return HttpResponse.json({ variables })
  }),
]
