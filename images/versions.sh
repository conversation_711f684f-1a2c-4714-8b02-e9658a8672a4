#!/bin/bash

TYPE="saas"
REGISTRY="$CI_REGISTRY"
VITE_USE_SENTRY="1"

if [[ -n $CI_COMMIT_TAG ]]
then
    RELEASE=$(echo $CI_COMMIT_TAG | grep -oE '\+[a-zA-Z]+' | sed 's/\+//')
    METATAG=""
    BUILD_1_MODE="production"
    BUILD_2_MODE=""
    EXTERNAL_QUIZ="false"
    if [[ "$CI_COMMIT_TAG" != *".sentry"* ]];
    then
      VITE_USE_SENTRY="0"
    fi
    if [[ -n $RELEASE ]]
    then
      METATAG="$RELEASE-"
      if [[ "$CI_COMMIT_TAG" = *".sentry"* ]];
      then
         METATAG="$METATAG-sentry-"
      fi
    else
      RELEASE="main"
    fi
    T=$(echo $CI_COMMIT_TAG | grep -oE '(Ver-)?[0-9]+\.[0-9]+\.[0-9]+' | sed 's/Ver-//')
    MODE="production"
    if [ "$RELEASE" = "mi" ] ||  [ "$RELEASE" = "mwi" ] ||  [ "$RELEASE" = "mqi" ]  ||  [ "$RELEASE" = "mqwi" ]
    then
      TYPE="instance"
    fi
    if [ "$RELEASE" = "mi" ]
    then
      REGISTRY="$CI_REGISTRY_MI"
    fi
    if [ "$RELEASE" = "mwi" ]
    then
      REGISTRY="$CI_REGISTRY_MWI"
    fi
    if [ "$RELEASE" = "mqi" ]
    then
      REGISTRY="$CI_REGISTRY_MQI"
      EXTERNAL_QUIZ="true"
    fi
    if [ "$RELEASE" = "mqwi" ]
    then
      REGISTRY="$CI_REGISTRY_MQWI"
      EXTERNAL_QUIZ="true"
    fi
    if [ "$RELEASE" = "main" ]
    then
      REGISTRY="$CI_REGISTRY_MWI"
      BUILD_1_MODE="production"
      BUILD_2_MODE=""
      VITE_USE_SENTRY="1"
      TAG="$T"
    else
      TAG="$T-$METATAG$CI_PIPELINE_IID"
    fi
else
    if [ "$CI_COMMIT_BRANCH" = "release" ] ||  [ "$CI_COMMIT_BRANCH" = "bugfixes" ]
    then
      v=$(curl -Ss --request GET --header "PRIVATE-TOKEN: $GIT_TOKEN" "https://doxsw.gitlab.yandexcloud.net/api/v4/projects/doxsw%2Ffoquz-quiz/repository/tags"  | jq -r '.[0] | .name')
      v1=$(echo $v | awk -F'.' '{print $1}')
      v2=$(echo $v | awk -F'.' '{print $2}')
      v3=$(echo $v | awk -F'.' '{print $3}')
      v1=${v1:4:5}
      if [ "$CI_COMMIT_BRANCH" = "release" ]
      then
        let v2=v2+1
        TAG="$v1.$v2.0-dev-$CI_PIPELINE_IID"
        RELEASE="release"
        MODE="development"
      fi
      if [ "$CI_COMMIT_BRANCH" = "bugfixes" ]
      then
        let v3=v3+1
        TAG="$v1.$v2.$v3-dev-$CI_PIPELINE_IID"
        RELEASE="bugfixes"
        MODE="development"
      fi
    else
        BRANCH=$(echo $CI_COMMIT_BRANCH | awk 'match($0, /TASK-[0-9]+?/) { print substr( $0, RSTART+5, RLENGTH-5 )}' )
        TAG=$(echo "task-$BRANCH-$CI_PIPELINE_IID")
        RELEASE=$(echo "task-$BRANCH")
        MODE="development"
    fi
    EXTERNAL_QUIZ=$(curl -s "http://deploy.devfoquz.ru/get_modules.php?release=$RELEASE&module=quiz")
fi

if [ "$EXTERNAL_QUIZ" = "false" ] && [ "$MODE" = "development" ]
then
    BUILD_1_MODE="staging"
    BUILD_2_MODE=""
elif [ "$EXTERNAL_QUIZ" = "false" ] && [ "$MODE" = "production" ]
then
    BUILD_1_MODE="production"
    BUILD_2_MODE=""
elif [ "$EXTERNAL_QUIZ" = "true" ] && [ "$MODE" = "development" ]
then
    BUILD_1_MODE="staging-preview"
    BUILD_2_MODE="staging"
elif [ "$EXTERNAL_QUIZ" = "true" ] && [ "$MODE" = "production" ]
then
    BUILD_1_MODE="production-preview"
    BUILD_2_MODE="production"
fi

echo "TAG=$TAG"
echo "TYPE=$TYPE"
echo "RELEASE=$RELEASE"
echo "MODE=$MODE"
echo "REGISTRY=$REGISTRY"
echo "EXTERNAL_QUIZ=$EXTERNAL_QUIZ"
echo "BUILD_1_MODE=$BUILD_1_MODE"
echo "BUILD_2_MODE=$BUILD_2_MODE"
echo "VITE_USE_SENTRY=$VITE_USE_SENTRY"

echo "TAG=$TAG" > build.env
echo "RELEASE=$RELEASE" >> build.env
echo "TYPE=$TYPE" >> build.env
echo "MODE=$MODE" >> build.env
echo "REGISTRY=$REGISTRY" >> build.env
echo "BUILD_1_MODE=$BUILD_1_MODE" >> build.env
echo "BUILD_2_MODE=$BUILD_2_MODE" >> build.env
echo "VITE_USE_SENTRY=$VITE_USE_SENTRY" >> build.env