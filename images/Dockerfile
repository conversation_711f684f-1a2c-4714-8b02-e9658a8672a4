FROM  cr.yandex/crpsvv5iscafkhlubkmt/node:20-alpine AS node

ARG TYPE
ARG BUILD_1_MODE
ARG BUILD_2_MODE
ARG VITE_USE_SENTRY

COPY ./ /app

RUN cd /app \
    && export NODE_ENV=${TYPE} \
    && export VITE_USE_SENTRY=${VITE_USE_SENTRY} \
    && npm install \
    && npm run build -- --mode ${BUILD_1_MODE} \
    && rm -R /app/dist/mocked-assets  \
    && rm  /app/dist/mockServiceWorker.js

COPY ./ /app2

RUN mkdir -p /app2 \
    && if [ -n "${BUILD_2_MODE}" ]; then \
        cd /app2 \
        && export NODE_ENV=${TYPE} \
        && export VITE_USE_SENTRY=${VITE_USE_SENTRY} \
        && npm install \
        && npm run build -- --mode ${BUILD_2_MODE} \
        && rm -R /app2/dist/mocked-assets \
        && rm /app2/dist/mockServiceWorker.js; \
    else \
        mkdir -p /app2/dist; \
    fi

FROM cr.yandex/crpsvv5iscafkhlubkmt/node:20-alpine

COPY --from=node  /app/dist /jsbuild
COPY --from=node  /app2/dist /jsbuild-quiz
