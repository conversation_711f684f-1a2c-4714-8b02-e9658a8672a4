<?php

$asset = \app\modules\foquz\assets\FoquzAsset::register($this);

use yii\helpers\Url;

$isEditor = Yii::$app->user->identity->isEditor();
$additionalFonts = [];
$pname = $poll->name;
$poll->name = addcslashes($poll->name, "\0..\37'\\");

if ($_SERVER["HTTP_HOST"] == "delimobil.foquz.ru") {
    $additionalFonts[] = 'Euclid Circular A, sans-serif';
}

if ($_SERVER["HTTP_HOST"] == "timeless.foquz.ru") {
    $additionalFonts[] = 'DX-Rigraf, sans-serif';
}

if ($_SERVER["HTTP_HOST"] == "r-ulybka.foquz.ru" || $_SERVER["HTTP_HOST"] == "ulybka-radugi.foquz.ru") {
    $additionalFonts[] = 'TT Norms, sans-serif';
}

if ($_SERVER["HTTP_HOST"] == "agrupp.foquz.ru") {
    $additionalFonts[] = 'Raleway, sans-serif';
}

$enableALSHaussFont = in_array($_SERVER["HTTP_HOST"], [
    "cubicmedia.foquz.ru",
    "m2.foquz.ru",
    "m2.doxswf.ru",
]);

if ($enableALSHaussFont) {
    $additionalFonts[] = 'ALSHauss, sans-serif';
}

if ($_SERVER["HTTP_HOST"] == "fabrika.foquz.ru") {
    $additionalFonts[] = 'Nordeco Cyrillic, sans-serif';
}

if ($_SERVER["HTTP_HOST"] == "dnsgroup.foquz.ru") {
  $additionalFonts[] = 'Benzin, sans-serif';
  $additionalFonts[] = 'Proxima Nova, sans-serif';
}

if ($_SERVER["HTTP_HOST"] == "start.foquz.ru" || $_SERVER["HTTP_HOST"] == "doxswf.ru") {
    $additionalFonts[] = 'Druk Wide, sans-serif';
}

if (isset(\Yii::$app->params['instance']) && \Yii::$app->params['instance']=="systeme") {
    $additionalFonts[] = 'OpenSans, sans-serif';
}


$startScreen = \yii\helpers\Json::encode($poll->startPage);
$endScreen = \yii\helpers\Json::encode($poll->endPage);

$this->registerJs("
    var TEMPLATES = " . \yii\helpers\Json::encode($templates) . ";
    var DESIGN = " . \yii\helpers\Json::encode($design) . ";
    var POLL = " . \yii\helpers\Json::encode($poll) . ";
    var POLL_NAME = '" . $poll->name  . "';
    var POLL_ID = " . $poll->id . ";
    var ACCESS_TOKEN = '" . Yii::$app->user->identity->access_token . "';
    var HAS_LOGIC = " . (int) $hasLogic . ";
    
    var PAGE_DATA = {
        additionalFonts: " . \yii\helpers\Json::encode($additionalFonts) . "
    };

    window.START_SCREEN = " . $startScreen . " || {};
    window.END_SCREEN = " . $endScreen . " || {};

    const interscreensVisible = window.START_SCREEN.enabled || window.END_SCREEN.enabled;

", $this::POS_HEAD);

$this->registerCSSFile('/js/poll.design.css', ['depends' => [app\modules\foquz\assets\FoquzAsset::className()]]);

$this->registerJSFile('/js/poll.design.js', ['depends' => [app\modules\foquz\assets\FoquzAsset::className()]]);


$poll->name = $pname;
$this->title = $poll->name;



?>

<div class="poll-design">

    <div class="settings__content settings__content--initializing" data-bind="childrenComplete: onInit">
        <!-- ko let: { pollName: ko.observable(POLL_NAME)} -->

        <!-- ko let: { onChangePollName: function(newName) {
            pollName(newName)
        } } -->
        <?= $this->render('../foquz-poll/_poll_header', [
            'model' => $poll,
            'page' => 'questions'
        ]) ?>
        <!-- /ko -->

        <?= $this->render('_menu_quests', ['page' => 'design', 'model' => $poll]) ?>


        <div class="content content--wo-bg p-0 ">
            <div class="f-tabs">
                <div data-bind="visible: !window.CURRENT_USER.watcher">
                    <nav class="nav f-tabs__nav" data-bind="visible: interscreensVisible">
                        <a class="nav-item nav-link  f-tabs__nav-item active" href="<?= Url::to(['/foquz/foquz-poll/custom-design', 'id' => $poll->id]) ?>" aria-selected="true">
                            <?= \Yii::t('design', 'Дизайн') ?>
                        </a>
                        <a class="nav-item nav-link f-tabs__nav-item" href="<?= Url::to(['/foquz/foquz-poll/interscreens', 'id' => $poll->id]) ?>" aria-selected="false">
                            <?= \Yii::t('design', 'Промежуточные экраны') ?>
                        </a>
                    </nav>
                </div>

                <div class="f-tabs__content">

                    <div class="f-tab tab-pane show active">

                        <div class="d-flex gap-4 align-items-start">

                            <div class="pt-20p flex-grow-1 f-card f-card--shadow f-card--lg f-card--min-height mr-20p" style="max-width: calc(100% - 390px)">

                                <div class="poll-design__content">
                                    <?php if (!(Yii::$app->user->identity->isFilialEmployee()) && !(Yii::$app->user->identity->isWatcher())) : ?>
                                        <div class="f-fs-1 f-color-service mb-3"><?= \Yii::t('design', 'В этом разделе можно настроить внешний вид опроса, а также сохранить настройки дизайна как тему. Используйте темы для быстрой настройки дизайна опроса.') ?></div>
                                    <?php endif; ?>

                                    <!-- ko ifnot: blocked -->
                                    <div class="form-group form-group_bordered">

                                        <label class="form-label">
                                            <span><?= \Yii::t('design', 'Тема') ?></span>
                                            <button type="button" class="btn-question" data-toggle="tooltip" data-placement="top" title="<?= \Yii::t('design', 'Внешний вид опроса можно сохранить как тему. Темы используются для быстрой настройки дизайна опроса') ?>"></button>
                                        </label>



                                        <div class="pb-2">
                                            <div data-bind="fScrollbar: { gradient: true, onlyX: true }" class="poll-design__themes">
                                                <div class="d-flex">
                                                    <!-- ko foreach: {
                                                        data: arThemes,
                                                        beforeRemove: beforeRemove,
                                                        afterAdd: afterAdd
                                                    } -->
                                                    <!-- ko template: { name: 'theme-preview-template' } -->
                                                    <!-- /ko -->
                                                    <!-- /ko -->
                                                </div>
                                            </div>

                                        </div>

                                    </div>
                                    <!-- /ko -->


                                    <section class="form-group form-group_bordered" data-bind="let: { opened: sections.colors.opened },
                                        dnd: function(files) {
                                            bgLoader.loadFile(files[0]);
                                        },
                                        dndDisabled: bgLoader.preview() || blocked,
                                        event: {
                                            'dnd.dragenter': function() {
                                                sections.colors.opened(true);
                                                return true;
                                            }
                                        }">
                                        <dnd-cover class="mt-n30p" params="type: 'image'"></dnd-cover>

                                        <h2 class="f-h2 cursor-pointer" data-bind="click: function() { opened(!opened()); }"><?= \Yii::t('design', 'Основные цвета и фон') ?>

                                            <span class="f-icon f-icon-arrow f-icon-arrow--top  f-transform--transition ml-2" data-bind="css: {
                                                'f-transform-rotate-180': !opened()
                                            }">
                                                <svg>
                                                    <use href="#arrow-top-icon"></use>
                                                </svg>
                                            </span>
                                        </h2>

                                        <!-- ko template: {
                                            foreach: templateIf(opened(), $data),
                                            afterAdd: slideAfterAddFactory(400),
                                            beforeRemove: slideBeforeRemoveFactory(400)
                                        } -->
                                        <div>
                                            <div class="d-flex poll-design__bg-loaders">
                                                <!-- ko ifnot: blocked && !bgLoader.preview() -->
                                                <div class="form-group mr-30p">
                                                    <media-load-button params="loader: bgLoader, disabled: blocked">
                                                        <svg-icon params="name: 'clip'" class="mb-5p"></svg-icon>
                                                        <?= \Yii::t('design', '.jpg .png<br>основное фоновое<br>изображение<br>(до 5Мб)') ?>
                                                    </media-load-button>
                                                    <file-loader-error params="error: bgLoader.error"></file-loader-error>
                                                </div>
                                                <!-- /ko -->

                                                <!-- ko ifnot: blocked && !mobileBgLoader.preview() -->
                                                <div class="form-group mr-30p">
                                                    <media-load-button params="loader: mobileBgLoader, disabled: blocked">
                                                        <svg-icon params="name: 'clip'" class="mb-5p"></svg-icon>
                                                        <?= \Yii::t('design', '.jpg .png<br>отдельный фон<br>для мобильного<br>вида (до 5Мб)') ?>
                                                    </media-load-button>
                                                    <file-loader-error params="error: mobileBgLoader.error"></file-loader-error>
                                                </div>
                                                <!-- /ko -->

                                                <!-- ko if: !blocked || bgLoader.preview() || mobileBgLoader.preview() -->
                                                <div class="service-text d-flex align-items-center" style="height: 105px;">
                                                    <!-- ko ifnot: blocked -->
                                                    Вы можете загрузить отдельный фон для мобильного вида опроса.
                                                    <!-- /ko -->
                                                    Если загружено только основное фоновое изображение, оно применяется для всех разрешений.
                                                </div>
                                                <!-- /ko -->

                                            </div>

                                            <div class="form-group">
                                                <switch style="margin-top: 10px" params="checked: activeTheme().darkening_background, disabled: blocked">Затемнение фона</switch>
                                            </div>


                                            <div class="row">
                                                <!-- цвет фона -->
                                                <div class="col-6">

                                                    <div class="form-group">
                                                        <label class="d-flex align-items-center justify-content-between form-label">
                                                            Цвет фона
                                                        </label>
                                                        <div class="input-group" data-bind="colorPicker">
                                                            <div class="input-group-prepend  poll-design__color-preview js-color-preview" data-bind="attr: {'data-backup': activeTheme().bgColor}">
                                                                <div class="checkerboard f-1"></div>
                                                                <div class="checkerboard f-2"></div>
                                                                <div class="checkerboard f-3"></div>
                                                                <div class="checkerboard f-4"></div>
                                                                <div class="checkerboard f-5"></div>
                                                                <div class="checkerboard f-6"></div>
                                                                <div class="checkerboard f-7"></div>
                                                                <div class="checkerboard f-8"></div>
                                                            </div>
                                                            <input required class="form-control" data-bind="textInput: activeTheme().bgColor, disable: blocked" data-color-format="rgba" maxlength="7">
                                                        </div>
                                                        <div class="poll-design__note">
                                                            Цвет фона для всей страницы, отображается, если не выбрано фоновое изображение
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="col-6">
                                                    <div class="form-group">
                                                        <label class="form-label">
                                                            Подложка под кнопками
                                                        </label>
                                                        <buttons-group params="
                                                                value: activeTheme().placeUnderButtons,
                                                                disabled: blocked,
                                                                buttons: [
                                                                    { value: 'dark', text: 'Темная' },
                                                                    { value: 'light', text: 'Светлая' },
                                                                ],
                                                            "></buttons-group>
                                                    </div>
                                                </div>



                                                <!-- основной цвет -->
                                                <div class="col-6">

                                                    <div class="form-group">
                                                        <label class="d-flex align-items-center justify-content-between form-label">
                                                            Основной цвет
                                                        </label>
                                                        <div class="input-group" data-bind="colorPicker">
                                                            <div class="input-group-prepend poll-design__color-preview js-color-preview">
                                                                <div class="checkerboard f-1"></div>
                                                                <div class="checkerboard f-2"></div>
                                                                <div class="checkerboard f-3"></div>
                                                                <div class="checkerboard f-4"></div>
                                                                <div class="checkerboard f-5"></div>
                                                                <div class="checkerboard f-6"></div>
                                                                <div class="checkerboard f-7"></div>
                                                                <div class="checkerboard f-8"></div>
                                                            </div>
                                                            <input required class="form-control" data-bind="textInput: activeTheme().mainColor, disable: blocked" data-color-format="rgba">
                                                        </div>
                                                        <div class="poll-design__note">
                                                            Цвет обводки для кнопки «Далее» и «Начать опрос», номера текущего вопроса, выбранного варианта ответа
                                                        </div>
                                                    </div>

                                                </div>

                                                <!-- цвет ссылок -->
                                                <div class="col-6">
                                                    <div class="form-group">
                                                        <label class="d-flex align-items-center justify-content-between form-label">
                                                            Цвет ссылок
                                                        </label>
                                                        <div class="input-group" data-bind="colorPicker">
                                                            <div class="input-group-prepend  poll-design__color-preview js-color-preview">
                                                                <div class="checkerboard f-1"></div>
                                                                <div class="checkerboard f-2"></div>
                                                                <div class="checkerboard f-3"></div>
                                                                <div class="checkerboard f-4"></div>
                                                                <div class="checkerboard f-5"></div>
                                                                <div class="checkerboard f-6"></div>
                                                                <div class="checkerboard f-7"></div>
                                                                <div class="checkerboard f-8"></div>
                                                            </div>
                                                            <input required class="form-control" data-bind="textInput: activeTheme().linkColor, disable: blocked" data-color-format="rgba" maxlength="7">
                                                        </div>
                                                        <div class="poll-design__note">
                                                            Цвет для ссылок «Отписаться от рассылки», «Пожаловаться», копирайта
                                                        </div>
                                                    </div>
                                                </div>

                                                <!-- цвет текста на фоне -->
                                                <div class="col-6">
                                                    <div class="form-group">
                                                        <label class="d-flex align-items-center justify-content-between form-label">
                                                            Цвет текста на фоне
                                                        </label>
                                                        <div class="input-group" data-bind="colorPicker">
                                                            <div class="input-group-prepend  poll-design__color-preview js-color-preview">
                                                                <div class="checkerboard f-1"></div>
                                                                <div class="checkerboard f-2"></div>
                                                                <div class="checkerboard f-3"></div>
                                                                <div class="checkerboard f-4"></div>
                                                                <div class="checkerboard f-5"></div>
                                                                <div class="checkerboard f-6"></div>
                                                                <div class="checkerboard f-7"></div>
                                                                <div class="checkerboard f-8"></div>
                                                            </div>
                                                            <input required class="form-control" data-bind="textInput: activeTheme().textOnBg, disable: blocked" data-color-format="rgba" maxlength="7">
                                                        </div>
                                                        <div class="poll-design__note">
                                                            Цвет вопроса, наименований вопросов, номеров вопросов, обводки кнопки «Вернуться»
                                                        </div>
                                                    </div>
                                                </div>

                                                <!-- цвет текста на подложке -->
                                                <div class="col-6">
                                                    <div class="form-group">
                                                        <label class="d-flex align-items-center justify-content-between form-label">
                                                            Цвет текста на подложке
                                                        </label>
                                                        <div class="input-group" data-bind="colorPicker">
                                                            <div class="input-group-prepend  poll-design__color-preview js-color-preview">
                                                                <div class="checkerboard f-1"></div>
                                                                <div class="checkerboard f-2"></div>
                                                                <div class="checkerboard f-3"></div>
                                                                <div class="checkerboard f-4"></div>
                                                                <div class="checkerboard f-5"></div>
                                                                <div class="checkerboard f-6"></div>
                                                                <div class="checkerboard f-7"></div>
                                                                <div class="checkerboard f-8"></div>
                                                            </div>
                                                            <input required class="form-control" data-bind="textInput: activeTheme().textOnPlace, disable: blocked" data-color-format="rgba" maxlength="7">
                                                        </div>
                                                        <div class="poll-design__note">
                                                            Цвет текста на подложке: варианты ответов, подписи изображений и т. д.
                                                        </div>
                                                    </div>
                                                </div>

                                                <!-- Цвет основной подложки -->
                                                <div class="col-6">
                                                    <div class="form-group">
                                                        <label class="d-flex align-items-center justify-content-between form-label">
                                                            Цвет основной подложки
                                                        </label>
                                                        <div class="input-group" data-bind="colorPicker">
                                                            <div class="input-group-prepend  poll-design__color-preview js-color-preview">
                                                                <div class="checkerboard f-1"></div>
                                                                <div class="checkerboard f-2"></div>
                                                                <div class="checkerboard f-3"></div>
                                                                <div class="checkerboard f-4"></div>
                                                                <div class="checkerboard f-5"></div>
                                                                <div class="checkerboard f-6"></div>
                                                                <div class="checkerboard f-7"></div>
                                                                <div class="checkerboard f-8"></div>
                                                            </div>
                                                            <input required class="form-control" data-bind="textInput: activeTheme().main_place_color, disable: blocked" data-color-format="rgba">
                                                        </div>
                                                        <div class="poll-design__note">
                                                            Цвет фона для подложки, на которой отображается содержимое вопроса: шкала, вариаты ответов, поля ввода и т.д.
                                                        </div>
                                                    </div>
                                                </div>

                                            </div>

<!--                                            <div class="form-group">-->
<!--                                                <switch style="margin-top: 10px" params="checked: activeTheme().fullWidth, disabled: blocked">Показать прохождение на всю ширину экрана</switch>-->
<!--                                            </div>-->
                                        </div>
                                        <!-- /ko -->
                                    </section>
                                    <section
                                        class="form-group form-group_bordered"
                                        data-bind="
                                            let: { opened: sections.cover.opened },
                                            dnd: function(files) {
                                                coverLoader.loadFile(files[0]);
                                            },
                                            dndDisabled: coverLoader.preview() || blocked,
                                            event: {
                                                'dnd.dragenter': function() {
                                                    sections.cover.opened(true);
                                                    return true;
                                                }
                                            }
                                        "
                                    >
                                        <dnd-cover class="mt-n30p" params="type: 'image'"></dnd-cover>
                                        <div class="form-group">
                                            <div class="poll-design__section-name">
                                                <div class="form-group switch-form-group d-flex align-items-center">
                                                    <label class="switch form-control">
                                                        <input
                                                            type="checkbox"
                                                            data-bind="checked: activeTheme().in_use_cover, disable: blocked"
                                                        >
                                                        <span class="switch__slider"></span>
                                                    </label>
                                                    <h2
                                                        class="f-h2 p-0 cursor-pointer d-flex ml-3 align-items-center"
                                                        data-bind="
                                                            click: () => { if (activeTheme().in_use_cover()) opened(!opened()); },
                                                        "
                                                    >

                                                        <?= \Yii::t('design', 'Обложка (верхний колонтитул)') ?>

                                                        <!-- ko if: activeTheme().in_use_cover -->
                                                        <span
                                                            class="f-icon f-icon-arrow f-icon-arrow--top  f-transform--transition ml-3"
                                                            data-bind="css: { 'f-transform-rotate-180': !opened() }"
                                                        >
                                                            <svg>
                                                                <use href="#arrow-top-icon"></use>
                                                            </svg>
                                                        </span>
                                                        <!-- /ko -->
                                                    </h2>
                                                </div>
                                            </div>
                                        </div>
                                        <!-- ko template: {
                                            foreach: templateIf(opened() && activeTheme().in_use_cover(), $data),
                                            afterAdd: slideAfterAddFactory(400),
                                            beforeRemove: slideBeforeRemoveFactory(400)
                                        } -->
                                            <div class="">
                                                <!-- ko ifnot: blocked && !coverLoader.preview() -->
                                                <div class="form-group d-flex align-items-center poll-design__cover-loader">
                                                    <div class="flex-grow-1 mr-15p">
                                                        <media-load-button
                                                            style="width: auto"
                                                            params="
                                                                loader: coverLoader,
                                                                disabled: blocked,
                                                                objectPosition: activeTheme().cover_position
                                                            "
                                                        >
                                                            <svg-icon params="name: 'clip'" class="mb-5p"></svg-icon>
                                                            <?= \Yii::t('design', '.jpg .png<br>Минимальный размер изображения — не менее 680х160 px (17:4).<br>Рекомендуемый размер для опции «растянуть на всю ширину» — не менее 1280х160 px.<br>Размер файла - до 5Мб') ?>
                                                        </media-load-button>
                                                        <file-loader-error params="error: coverLoader.error"></file-loader-error>
                                                    </div>
                                                    <div class="d-flex flex-column">
                                                        <button
                                                            class="button-ghost poll-design__icon"
                                                            data-bind="
                                                                tooltip,
                                                                tooltipPlacement: 'top',
                                                                tooltipText: 'Верхняя область изображения',
                                                                css: { 'poll-design__icon_active': activeTheme().cover_position() === 0 },
                                                                attr: { disabled: !coverLoader.preview() },
                                                                click: () => { if (!blocked) activeTheme().cover_position(0) },
                                                            "
                                                        >
                                                            <svg-icon params="name: 'positioning-top'"></svg-icon>
                                                        </button>
                                                        <button
                                                            class="button-ghost poll-design__icon my-15p"
                                                            data-bind="
                                                                tooltip,
                                                                tooltipPlacement: 'top',
                                                                tooltipText: 'Центральная область изображения',
                                                                css: { 'poll-design__icon_active': activeTheme().cover_position() === 1 },
                                                                attr: { disabled: !coverLoader.preview() },
                                                                click: () => { if (!blocked) activeTheme().cover_position(1) },
                                                            "
                                                        >
                                                            <svg-icon params="name: 'positioning-center'"></svg-icon>
                                                        </button>
                                                        <button
                                                            class="button-ghost poll-design__icon"
                                                            data-bind="
                                                                tooltip,
                                                                tooltipPlacement: 'top',
                                                                tooltipText: 'Нижняя область изображения',
                                                                css: { 'poll-design__icon_active': activeTheme().cover_position() === 2 },
                                                                attr: { disabled: !coverLoader.preview() },
                                                                click: () => { if (!blocked) activeTheme().cover_position(2) },
                                                            "
                                                        >
                                                            <svg-icon params="name: 'positioning-bottom'"></svg-icon>
                                                        </button>
                                                    </div>
                                                </div>
                                                <!-- /ko -->
                                                <div class="row">
                                                    <div class="col-12 col-lg-6">
                                                        <div class="form-group">
                                                            <input-checkbox params="checked: activeTheme().cover_only_first_page, disabled: blocked">
                                                                Показывать только на первой странице
                                                            </input-checkbox>
                                                        </div>
                                                    </div>
                                                    <div class="col-12 col-lg-6">
                                                        <div class="form-group">
                                                            <input-checkbox params="checked: activeTheme().cover_full_width, disabled: blocked">
                                                                Растянуть на ширину экрана
                                                            </input-checkbox>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        <!-- /ko -->
                                    </section>
                                    <section class="form-group form-group_bordered" data-bind="let: { opened: sections.btnColors.opened },
                                        dnd: function(files) {
                                            bgLoader.loadFile(files[0]);
                                        },
                                        dndDisabled: bgLoader.preview() || blocked,
                                        event: {
                                            'dnd.dragenter': function() {
                                                sections.btnColors.opened(true);
                                                return true;
                                            }
                                        }">
                                        <dnd-cover class="mt-n30p" params="type: 'image'"></dnd-cover>

                                        <h2 class="f-h2 cursor-pointer" data-bind="click: function() { opened(!opened()); }"><?= \Yii::t('design', 'Основные кнопки') ?>
                                            <svg class="beta-icon">
                                                <use href="#beta-icon"></use>
                                            </svg>
                                            <span class="f-icon f-icon-arrow f-icon-arrow--top  f-transform--transition ml-2" data-bind="css: {
                                                'f-transform-rotate-180': !opened()
                                            }">

                                                <svg>
                                                    <use href="#arrow-top-icon"></use>
                                                </svg>
                                            </span>
                                        </h2>

                                        <!-- ko template: {
                                            foreach: templateIf(opened(), $data),
                                            afterAdd: slideAfterAddFactory(400),
                                            beforeRemove: slideBeforeRemoveFactory(400)
                                        } -->
                                        <div>
                                            <div class="row">
                                                <div class="col col-12">
                                                    <div class="form-group">
                                                        <fc-label params="text: 'Кнопки «Вперед» / «Завершить»', hint: 'Кнопки «Вперед» / «Завершить»'"></fc-label>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="row">
                                                <!-- Цвет фона кнопки -->
                                                <div class="col-6">
                                                    <div class="form-group">
                                                        <fc-label params="text: 'Цвет фона кнопки'"></fc-label>

                                                        <div class="input-group" data-bind="colorPicker">
                                                            <div class="input-group-prepend  poll-design__color-preview js-color-preview" data-bind="attr: {'data-backup': activeTheme().next_button_background_color}">
                                                                <div class="checkerboard f-1"></div>
                                                                <div class="checkerboard f-2"></div>
                                                                <div class="checkerboard f-3"></div>
                                                                <div class="checkerboard f-4"></div>
                                                                <div class="checkerboard f-5"></div>
                                                                <div class="checkerboard f-6"></div>
                                                                <div class="checkerboard f-7"></div>
                                                                <div class="checkerboard f-8"></div>
                                                            </div>
                                                            <input required class="form-control" data-bind="textInput: activeTheme().next_button_background_color, disable: blocked" maxlength="7" data-color-format="rgba">
                                                        </div>
                                                    </div>
                                                </div>

                                                <!-- Цвет текста -->
                                                <div class="col-6">

                                                    <div class="form-group">
                                                        <fc-label params="text: 'Цвет текста'"></fc-label>
                                                        <div class="input-group" data-bind="colorPicker">
                                                            <div class="input-group-prepend poll-design__color-preview js-color-preview">
                                                                <div class="checkerboard f-1"></div>
                                                                <div class="checkerboard f-2"></div>
                                                                <div class="checkerboard f-3"></div>
                                                                <div class="checkerboard f-4"></div>
                                                                <div class="checkerboard f-5"></div>
                                                                <div class="checkerboard f-6"></div>
                                                                <div class="checkerboard f-7"></div>
                                                                <div class="checkerboard f-8"></div>
                                                            </div>
                                                            <input required class="form-control" data-bind="textInput: activeTheme().next_button_text_color, disable: blocked" data-color-format="rgba" maxlength="7">
                                                        </div>
                                                    </div>

                                                </div>

                                                <!-- Цвет обводки -->
                                                <div class="col-6">
                                                    <div class="form-group">
                                                        <fc-label params="text: 'Цвет обводки'"></fc-label>
                                                        <div class="input-group" data-bind="colorPicker">
                                                            <div class="input-group-prepend  poll-design__color-preview js-color-preview">
                                                                <div class="checkerboard f-1"></div>
                                                                <div class="checkerboard f-2"></div>
                                                                <div class="checkerboard f-3"></div>
                                                                <div class="checkerboard f-4"></div>
                                                                <div class="checkerboard f-5"></div>
                                                                <div class="checkerboard f-6"></div>
                                                                <div class="checkerboard f-7"></div>
                                                                <div class="checkerboard f-8"></div>
                                                            </div>
                                                            <input required class="form-control" data-bind="textInput: activeTheme().next_button_stroke_color, disable: blocked" data-color-format="rgba" maxlength="7">
                                                        </div>
                                                    </div>
                                                </div>

                                                <!-- Радиус скругления углов, px -->
                                                <div class="col-6">
                                                    <div class="form-group">
                                                        <fc-label params="text: 'Радиус скругления углов, px'"></fc-label>
                                                        <input type="text" class="form-control" placeholder="0" data-bind="textInput: activeTheme().next_button_radius, disable: blocked" maxlength="40" />
                                                    </div>
                                                </div>
                                                <div class="col-6">
                                                    <div class="form-group">
                                                        <label class="form-label">
                                                            Текст правой кнопки «Вперед»
                                                        </label>
                                                        <input type="text" class="form-control" placeholder="Далее" data-bind="textInput: activeTheme().nextText, disable: blocked" maxlength="40" />
                                                    </div>
                                                </div>
                                                <div class="col-6">
                                                    <div class="form-group">
                                                        <fc-label params="text: 'Текст кнопки «Завершить»', hint: 'Текст кнопки «Завершить»'"></fc-label>
                                                        <input type="text" class="form-control" placeholder="Завершить" data-bind="textInput: activeTheme().finishText, disable: blocked" maxlength="40" />
                                                    </div>
                                                </div>
                                                <div class="col-12">
                                                    <div class="form-group mb-0">
                                                        <fc-label params="text: 'Переход по кнопке «Завершить»', hint: 'Переход по кнопке «Завершить»'"></fc-label>
                                                        <input type="text" class="form-control" placeholder="https://" data-bind="
                                                                textInput: activeTheme().finishLink,
                                                                disable: blocked,
                                                            " maxlength="2048" />
                                                        <div class="service-text mt-10p">Для стандартного перехода к конечному экрану оставьте поле пустым</div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <hr style="margin: 25px 0;"/>

                                        <div>
                                            <div class="row">
                                                <div class="col col-12">
                                                    <div class="form-group">
                                                        <fc-label params="text: 'Кнопка «Назад» / «Отчет о тестировании»', hint: 'Кнопка «Назад» / «Отчет о тестировании»'"></fc-label>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="row">
                                                <!-- Цвет фона кнопки -->
                                                <div class="col-6">
                                                    <div class="form-group">
                                                        <fc-label params="text: 'Цвет фона кнопки'"></fc-label>

                                                        <div class="input-group" data-bind="colorPicker">
                                                            <div class="input-group-prepend  poll-design__color-preview js-color-preview" data-bind="attr: {'data-backup': activeTheme().back_button_background_color}">
                                                                <div class="checkerboard f-1"></div>
                                                                <div class="checkerboard f-2"></div>
                                                                <div class="checkerboard f-3"></div>
                                                                <div class="checkerboard f-4"></div>
                                                                <div class="checkerboard f-5"></div>
                                                                <div class="checkerboard f-6"></div>
                                                                <div class="checkerboard f-7"></div>
                                                                <div class="checkerboard f-8"></div>
                                                            </div>
                                                            <input required class="form-control" data-bind="textInput: activeTheme().back_button_background_color, disable: blocked" maxlength="7" data-color-format="rgba">
                                                        </div>
                                                    </div>
                                                </div>

                                                <!-- Цвет текста -->
                                                <div class="col-6">

                                                    <div class="form-group">
                                                        <fc-label params="text: 'Цвет текста'"></fc-label>
                                                        <div class="input-group" data-bind="colorPicker">
                                                            <div class="input-group-prepend poll-design__color-preview js-color-preview">
                                                                <div class="checkerboard f-1"></div>
                                                                <div class="checkerboard f-2"></div>
                                                                <div class="checkerboard f-3"></div>
                                                                <div class="checkerboard f-4"></div>
                                                                <div class="checkerboard f-5"></div>
                                                                <div class="checkerboard f-6"></div>
                                                                <div class="checkerboard f-7"></div>
                                                                <div class="checkerboard f-8"></div>
                                                            </div>
                                                            <input required class="form-control" data-bind="textInput: activeTheme().back_button_text_color, disable: blocked" data-color-format="rgba" maxlength="7">
                                                        </div>
                                                    </div>

                                                </div>

                                                <!-- Цвет обводки -->
                                                <div class="col-6">
                                                    <div class="form-group">
                                                        <fc-label params="text: 'Цвет обводки'"></fc-label>
                                                        <div class="input-group" data-bind="colorPicker">
                                                            <div class="input-group-prepend  poll-design__color-preview js-color-preview">
                                                                <div class="checkerboard f-1"></div>
                                                                <div class="checkerboard f-2"></div>
                                                                <div class="checkerboard f-3"></div>
                                                                <div class="checkerboard f-4"></div>
                                                                <div class="checkerboard f-5"></div>
                                                                <div class="checkerboard f-6"></div>
                                                                <div class="checkerboard f-7"></div>
                                                                <div class="checkerboard f-8"></div>
                                                            </div>
                                                            <input required class="form-control" data-bind="textInput: activeTheme().back_button_stroke_color, disable: blocked" data-color-format="rgba" maxlength="7">
                                                        </div>
                                                    </div>
                                                </div>

                                                <!-- Радиус скругления углов, px -->
                                                <div class="col-6">
                                                    <div class="form-group">
                                                        <fc-label params="text: 'Радиус скругления углов, px'"></fc-label>
                                                        <input type="text" class="form-control" placeholder="0" data-bind="textInput: activeTheme().back_button_radius, disable: blocked" maxlength="40" />
                                                    </div>
                                                </div>
                                                <div class="col col-12">
                                                    <div class="form-group">
                                                        <input-checkbox params="checked: activeTheme().show_prev_button, disabled: blocked">
                                                            Показывать левую кнопку «Назад» <question-button params="text: 'Показывать кнопку «Назад»'"></question-button>
                                                        </input-checkbox>
                                                    </div>
                                                </div>
                                                <div class="col col-6">
                                                    <div class="form-group mb-0">
                                                        <label class="form-label">
                                                            Текст левой кнопки «Назад»
                                                        </label>
                                                        <input type="text" class="form-control" placeholder="Вернуться" data-bind="textInput: activeTheme().backText, disable: blocked || !activeTheme().show_prev_button()" maxlength="40" />
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <hr style="margin: 25px 0;"/>

                                        <div>
                                            <div class="row">
                                                <div class="col col-12">
                                                    <div class="form-group">
                                                        <fc-label params="text: 'Кнопки «Пройти опрос» / «Начать заново» / «Готово» / «Закрыть» (для виджета) / «Показать изображение»', hint: 'Кнопки «Пройти опрос» / «Начать заново» / «Готово» / «Закрыть» (для виджета) / «Показать изображение»'"></fc-label>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="row">
                                                <!-- Цвет фона кнопки -->
                                                <div class="col-6">
                                                    <div class="form-group">
                                                        <fc-label params="text: 'Цвет фона кнопки'"></fc-label>

                                                        <div class="input-group" data-bind="colorPicker">
                                                            <div class="input-group-prepend  poll-design__color-preview js-color-preview" data-bind="attr: {'data-backup': activeTheme().start_button_background_color}">
                                                                <div class="checkerboard f-1"></div>
                                                                <div class="checkerboard f-2"></div>
                                                                <div class="checkerboard f-3"></div>
                                                                <div class="checkerboard f-4"></div>
                                                                <div class="checkerboard f-5"></div>
                                                                <div class="checkerboard f-6"></div>
                                                                <div class="checkerboard f-7"></div>
                                                                <div class="checkerboard f-8"></div>
                                                            </div>
                                                            <input required class="form-control" data-bind="textInput: activeTheme().start_button_background_color, disable: blocked" maxlength="7" data-color-format="rgba">
                                                        </div>
                                                    </div>
                                                </div>

                                                <!-- Цвет текста -->
                                                <div class="col-6">

                                                    <div class="form-group">
                                                        <fc-label params="text: 'Цвет текста'"></fc-label>
                                                        <div class="input-group" data-bind="colorPicker">
                                                            <div class="input-group-prepend poll-design__color-preview js-color-preview">
                                                                <div class="checkerboard f-1"></div>
                                                                <div class="checkerboard f-2"></div>
                                                                <div class="checkerboard f-3"></div>
                                                                <div class="checkerboard f-4"></div>
                                                                <div class="checkerboard f-5"></div>
                                                                <div class="checkerboard f-6"></div>
                                                                <div class="checkerboard f-7"></div>
                                                                <div class="checkerboard f-8"></div>
                                                            </div>
                                                            <input required class="form-control" data-bind="textInput: activeTheme().start_button_text_color, disable: blocked" data-color-format="rgba" maxlength="7">
                                                        </div>
                                                    </div>

                                                </div>

                                                <!-- Цвет обводки -->
                                                <div class="col-6">
                                                    <div class="form-group">
                                                        <fc-label params="text: 'Цвет обводки'"></fc-label>
                                                        <div class="input-group" data-bind="colorPicker">
                                                            <div class="input-group-prepend  poll-design__color-preview js-color-preview">
                                                                <div class="checkerboard f-1"></div>
                                                                <div class="checkerboard f-2"></div>
                                                                <div class="checkerboard f-3"></div>
                                                                <div class="checkerboard f-4"></div>
                                                                <div class="checkerboard f-5"></div>
                                                                <div class="checkerboard f-6"></div>
                                                                <div class="checkerboard f-7"></div>
                                                                <div class="checkerboard f-8"></div>
                                                            </div>
                                                            <input required class="form-control" data-bind="textInput: activeTheme().start_button_stroke_color, disable: blocked" data-color-format="rgba" maxlength="7">
                                                        </div>
                                                    </div>
                                                </div>

                                                <!-- Радиус скругления углов, px -->
                                                <div class="col-6">
                                                    <div class="form-group">
                                                        <fc-label params="text: 'Радиус скругления углов, px'"></fc-label>
                                                        <input type="text" class="form-control" placeholder="0" data-bind="textInput: activeTheme().start_button_radius, disable: blocked" maxlength="40" />
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <!-- /ko -->
                                    </section>

                                    <section class="form-group form-group_bordered" data-bind="let: { opened: sections.text.opened }">

                                        <h2 class="f-h2 cursor-pointer" data-bind="click: function() { opened(!opened()) }">
                                            Текст

                                            <span class="f-icon f-icon-arrow f-icon-arrow--top  f-transform--transition ml-2" data-bind="css: {
                                                'f-transform-rotate-180': !opened()
                                            }">
                                                <svg>
                                                    <use href="#arrow-top-icon"></use>
                                                </svg>
                                            </span>
                                        </h2>

                                        <!-- ko template: {
                                            foreach: templateIf(opened(), $data),
                                            afterAdd: slideAfterAddFactory(400),
                                            beforeRemove: slideBeforeRemoveFactory(400)
                                        } -->
                                        <div>

                                            <div class="row">
                                                <!-- наименование шрифта -->
                                                <div class="col-12">
                                                    <div class="form-group">
                                                        <label class="d-flex align-items-center justify-content-between form-label">
                                                            Наименование шрифта
                                                        </label>
                                                        <select data-bind="value: activeTheme().fontFamily,
                                                            options: fontsSet,
                                                            disable: blocked,
                                                            select2: {
                                                                containerCssClass: 'form-control',
                                                                wrapperCssClass: 'select2-container--form-control',
                                                                minimumResultsForSearch: 0
                                                            }">
                                                        </select>
                                                    </div>
                                                </div>

                                                <!-- размер заголовка -->
                                                <div class="col-6">
                                                    <div class="form-group">
                                                        <label class="d-flex align-items-center justify-content-between form-label">
                                                            Размер заголовка
                                                        </label>
                                                        <select data-bind="value: activeTheme().titleFontSize,
                                                        disable: blocked,
                                                        select2: {
                                                            containerCssClass: 'form-control',
                                                            wrapperCssClass: 'select2-container--form-control',
                                                            minimumResultsForSearch: 10
                                                        }
                                                        ">
                                                            <option value="16">16</option>
                                                            <option value="18">18</option>
                                                            <option value="20">20</option>
                                                            <option value="22">22</option>
                                                            <option value="24">24</option>
                                                            <option value="26">26</option>
                                                            <option value="28">28</option>
                                                            <option value="30">30</option>
                                                            <option value="32">32</option>
                                                            <option value="34">34</option>
                                                            <option value="36">36</option>
                                                            <option value="38">38</option>
                                                            <option value="40">40</option>
                                                            <option value="42">42</option>
                                                        </select>
                                                        <div class="poll-design__note">
                                                            Размер текста вопроса на фоне, размер заголовков для начального и конечного экранов
                                                        </div>
                                                    </div>
                                                </div>

                                                <!-- размер основного текста -->
                                                <div class="col-6">
                                                    <div class="form-group">
                                                        <label class="d-flex align-items-center justify-content-between form-label">
                                                            Размер основного текста
                                                        </label>
                                                        <select data-bind="value: activeTheme().fontSize,
                                                        disable: blocked,
                                                        select2: {
                                                                containerCssClass: 'form-control',
                                                                wrapperCssClass: 'select2-container--form-control',
                                                                minimumResultsForSearch: 10
                                                            }
                                                                ">
                                                            <option value="12">12</option>
                                                            <option value="13">13</option>
                                                            <option value="14">14</option>
                                                            <option value="15">15</option>
                                                            <option value="16">16</option>
                                                            <option value="17">17</option>
                                                            <option value="18">18</option>
                                                        </select>
                                                        <div class="poll-design__note">
                                                            Размер текста на белой подложке: варианты ответов, подписи изображений и т. д.
                                                        </div>
                                                    </div>
                                                </div>


                                            </div>

                                        </div>
                                        <!-- /ko -->
                                    </section>


                                    <section class="form-group form-group_bordered  mb-4" data-bind="let: {
                                        opened: sections.header.opened
                                        }, dnd: function(files) {
                                            logoLoader.loadFile(files[0]);
                                        },
                                        dndDisabled: blocked || logoLoader.preview() || activeTheme().isTextLogo(),
                                        event: {
                                            'dnd.dragenter': function() {
                                                sections.header.opened(true);
                                                return true;
                                            }
                                        }">

                                        <dnd-cover class="mt-n25p" params="type: 'image'"></dnd-cover>

                                        <div class="form-group">
                                            <div class="poll-design__section-name">

                                                <div class="form-group switch-form-group d-flex align-items-center">

                                                    <label class="switch form-control">
                                                        <input
                                                            type="checkbox"
                                                            id="is-use-header"
                                                            data-bind="
                                                                checked: activeTheme().isUseHeader,
                                                                disable: blocked,
                                                            "
                                                        >
                                                        <span class="switch__slider"></span>
                                                    </label>

                                                    <h2 class="f-h2 p-0 cursor-pointer d-flex ml-3 align-items-center" data-bind="
                                                            click: function() {
                                                                if (activeTheme().isUseHeader()) {
                                                                    opened(!opened());
                                                                }
                                                            },
                                                        ">
                                                        Шапка
                                                        <!-- ko if: activeTheme().isUseHeader -->

                                                        <span class="f-icon f-icon-arrow f-icon-arrow--top  f-transform--transition ml-3" data-bind="css: {
                                                            'f-transform-rotate-180': !opened()
                                                        }">
                                                            <svg>
                                                                <use href="#arrow-top-icon"></use>
                                                            </svg>
                                                        </span>
                                                        <!-- /ko -->
                                                    </h2>
                                                </div>
                                            </div>

                                        </div>

                                        <!-- ko template: {
                                            foreach: templateIf(activeTheme().isUseHeader() && opened(), $data),
                                            afterAdd: slideAfterAddFactory(200),
                                            beforeRemove: slideBeforeRemoveFactory(200)
                                        } -->
                                        <div id="header_collapse">
                                            <div class="row">
                                                <div class="col-6">
                                                    <div class="form-group">
                                                        <label class="d-flex align-items-center justify-content-between form-label">
                                                            Цвет шапки
                                                        </label>
                                                        <div class="input-group" data-bind="colorPicker">
                                                            <div class="input-group-prepend  poll-design__color-preview js-color-preview"></div>
                                                            <input required class="form-control" data-bind="textInput: activeTheme().headerColor, disable: blocked" data-color-format="rgba" maxlength="7">
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <label class="d-flex align-items-center justify-content-between form-label">
                                                <span>
                                                    Логотип
                                                    <button class="btn-question" data-bind="tooltip, tooltipPlacement: 'top'" type="button" title="Для логотипа можно использовать изображение или написать текст">
                                                    </button>
                                                </span>
                                            </label>

                                            <div class="row">
                                                <div class="col-auto" style="min-width: 50%">
                                                    <div class="form-group">
                                                        <div class="hat-radio-group form-control">
                                                            <div class="hat-radio-group__radio">
                                                                <input class="hat-radio-group__radio-input" type="radio" name="logo-type" value="image" id="logo-type-image" data-bind="checked: activeTheme().logoType, disable: blocked" />
                                                                <label class="hat-radio-group__radio-label" for="logo-type-image">
                                                                    <span class="hat-radio-group__radio-indicator"></span>
                                                                    Картинка
                                                                </label>
                                                            </div>

                                                            <div class="hat-radio-group__radio">
                                                                <input class="hat-radio-group__radio-input" type="radio" checked name="logo-type" value="text" id="logo-type-text" data-bind="checked: activeTheme().logoType, disable: blocked" />
                                                                <label class="hat-radio-group__radio-label" for="logo-type-text">
                                                                    <span class="hat-radio-group__radio-indicator"></span>
                                                                    Текст
                                                                </label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="col">
                                                    <div class="form-group poll-design__logo-loader">
                                                        <!-- ko template: {
                                                            foreach: templateIf(activeTheme().isTextLogo(), $data),
                                                            afterAdd: fadeAfterAddFactory(200)
                                                        } -->
                                                        <div class="input-group" style="min-width: 250px">
                                                            <input class="form-control" data-bind="textInput: activeTheme().logoText, disable: blocked" placeholder="Введите текст логотипа">
                                                        </div>
                                                        <!-- /ko -->

                                                        <!-- ko template: {
                                                            foreach: templateIf(!activeTheme().isTextLogo(), $data),
                                                            afterAdd: fadeAfterAddFactory(200)
                                                        } -->
                                                        <media-load-button class="media-load-button--logo media-load-button--transparent" params="loader: logoLoader, disabled: blocked">
                                                            <svg-icon params="name: 'clip'" class="mr-10p svg-icon--lg"></svg-icon>
                                                            .jpg .png .svg
                                                        </media-load-button>

                                                        <file-loader-error params="error: logoLoader.error"></file-loader-error>

                                                        <!-- /ko -->
                                                    </div>
                                                </div>
                                            </div>
                                            <!-- ko template: {
                                                foreach: templateIf(activeTheme().isTextLogo(), $data),
                                                afterAdd: slideAfterAddFactory(200),
                                                beforeRemove: slideBeforeRemoveFactory(200)
                                            } -->
                                            <div class="row">
                                                <div class="col-6">
                                                    <div class="form-group">
                                                        <label class="d-flex align-items-center justify-content-between form-label">
                                                            <span>
                                                                Шрифт
                                                                <button class="btn-question" data-bind="tooltip, tooltipPlacement: 'top'" type="button" title="Можно выбрать шрифт для текста логотипа">
                                                                </button>
                                                            </span>
                                                        </label>

                                                        <select data-bind="value: activeTheme().logoFontFamily,
                                                            options: fontsSet,
                                                            disable: blocked,
                                                            select2: {
                                                                containerCssClass: 'form-control',
                                                                wrapperCssClass: 'select2-container--form-control',
                                                                minimumResultsForSearch: 0
                                                            }">
                                                        </select>
                                                    </div>
                                                </div>
                                                <div class="col-4">
                                                    <div class="form-group">
                                                        <label for="" class="form-label"><?= \Yii::t('profile', 'Размер текста') ?></label>
                                                        <div class="d-flex align-items-center">
                                                            <collection-select class="select-font" params="collection: $parent.fontSizes, value: activeTheme().logo_text_size, search: true,disabled: blocked"></collection-select>
                                                            <button type="button" class="button-ghost button-font" data-bind="css: {
                                                            active: activeTheme().logo_text_bold
                                                            }, click: function() {
                                                            activeTheme().logo_text_bold(!activeTheme().logo_text_bold())
                                                            }, tooltip, tooltipText: _t('widget', 'Жирное начертание'),disable: blocked">
                                                                <svg-icon params="name: 'text-bold'"></svg-icon>
                                                            </button>
                                                            <button type="button" class="button-ghost button-font" data-bind="css: {
                                                            active: activeTheme().logo_text_italic
                                                            }, click: function() {
                                                                activeTheme().logo_text_italic(!activeTheme().logo_text_italic())
                                                            }, tooltip, tooltipText: _t('widget', 'Курсивное начертание'),disable: blocked">
                                                                <svg-icon params="name: 'text-italic'"></svg-icon>
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>

                                            </div>
                                            <div class="row">
                                                <div class="col-6">
                                                    <div class="form-group">
                                                        <label class="d-flex align-items-center justify-content-between form-label">
                                                            <span>
                                                                Цвет текста логотипа
                                                                <button class="btn-question" data-bind="tooltip, tooltipPlacement: 'top'" type="button" title="Можно выбрать цвет для текста логотипа">
                                                                </button>
                                                            </span>
                                                        </label>
                                                        <div class="input-group" data-bind="colorPicker">
                                                            <div class="input-group-prepend  poll-design__color-preview js-color-preview"></div>
                                                            <input required class="form-control" data-bind="textInput: activeTheme().logoColor, disable: blocked" maxlength="7" data-is-hex="true">
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <!-- /ko -->

                                            <div class="service-text mb-25p">Минимальная высота шапки — 45 px, максимальная ширина логотипа для мобильных экранов — 180 px</div>

                                            <label class="d-flex align-items-center justify-content-between form-label">
                                                <span>
                                                    Позиционирование логотипа
                                                    <button class="btn-question" data-bind="tooltip, tooltipPlacement: 'top'" type="button" title="озиционирование логотипа">
                                                    </button>
                                                </span>
                                            </label>

                                            <div class="row">
                                                <div class="col-12" style="min-width: 50%">
                                                    <div class="form-group">
                                                        <div class="hat-radio-group form-control">
                                                            <div class="hat-radio-group__radio">
                                                                <input class="hat-radio-group__radio-input" type="radio" name="logo-position" value="1" id="logo-type-position-left" data-bind="checked: activeTheme().logo_position, disable: blocked" />
                                                                <label class="hat-radio-group__radio-label" for="logo-type-position-left" data-bind="click: function () {
                                                                    console.log(activeTheme().logo_position())
                                                                    return true}">
                                                                    <span class="hat-radio-group__radio-indicator"></span>
                                                                    Слева
                                                                </label>
                                                            </div>

                                                            <div class="hat-radio-group__radio">
                                                                <input class="hat-radio-group__radio-input" type="radio" name="logo-position" value="2" id="logo-type-position-center" data-bind="checked: activeTheme().logo_position, disable: blocked" />
                                                                <label class="hat-radio-group__radio-label" for="logo-type-position-center">
                                                                    <span class="hat-radio-group__radio-indicator"></span>
                                                                    По центру
                                                                </label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="row">
                                                <!-- ko template: {
                                                            foreach: templateIf(!activeTheme().isTextLogo(), $data),
                                                            afterAdd: fadeAfterAddFactory(200)
                                                        } -->
                                                <div class="col-6">
                                                    <div class="form-group">
                                                        <label class="d-flex align-items-center justify-content-between form-label">
                                                            <span>
                                                                Высота логотипа, <span class="font-weight-normal">px</span>
                                                            </span>
                                                        </label>

                                                        <div class="input-group">
                                                            <input class="form-control text-center" data-bind="textInput: activeTheme().logo_height, disable: blocked" placeholder="">
                                                        </div>
                                                    </div>
                                                </div>
                                                <!-- /ko -->
                                                <div class="col-6">
                                                    <div class="form-group">
                                                        <label class="d-flex align-items-center justify-content-between form-label">
                                                            <span>
                                                                Отступы сверху/снизу, <span class="font-weight-normal">px</span>
                                                            </span>
                                                        </label>

                                                        <div class="input-group">
                                                            <input class="form-control text-center" data-bind="textInput: activeTheme().logo_margins, disable: blocked" placeholder="">
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="col">
                                                    <div class="form-group">
                                                        <input-checkbox params="checked: activeTheme().small_header_mobile, disabled: blocked">
                                                            Уменьшенный размер шапки для мобильных
                                                            <question-button params="text: 'Уменьшенный размер шапки для мобильных'"></question-button>
                                                        </input-checkbox>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="row">
                                                <div class="col-12">
                                                    <div class="form-group">
                                                        <label class="d-flex align-items-center form-label">
                                                            <span>Ссылка логотипа</span>

                                                            <button type="button" class="btn-question" data-toggle="tooltip" data-placement="top" title="Можно указать адрес сайта, к которому будет переход по клику на логотип"></button>

                                                        </label>
                                                        <div class="input-group">
                                                            <input class="form-control" data-bind="textInput: activeTheme().logoLink, disable: activeTheme().isLogoEmpty() || blocked" placeholder="http://">
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="col-12">
                                                    <div class="form-group">
                                                        <input-checkbox params="checked: activeTheme().chooseLanguage, disabled: blocked">
                                                            Выбор языка опроса <question-button params="text: 'Выбор языка опроса'"></question-button>
                                                        </input-checkbox>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <!-- /ko -->
                                    </section>




                                    <section class="form-group mb-4 form-group_bordered" data-bind="
                                            let: {
                                                opened: sections.navigation.opened,
                                            },
                                        ">
                                        <h2 class="f-h2 cursor-pointer" data-bind="
                                                click: function() {
                                                    opened(!opened());
                                                },
                                            ">
                                            Навигация
                                            <span class="f-icon f-icon-arrow f-icon-arrow--top f-transform--transition ml-2" data-bind="
                                                    css: {
                                                        'f-transform-rotate-180': !opened(),
                                                    },
                                                ">
                                                <svg>
                                                    <use href="#arrow-top-icon"></use>
                                                </svg>
                                            </span>
                                        </h2>
                                        <!-- ko template: {
                                            foreach: templateIf(opened(), $data),
                                            afterAdd: slideAfterAddFactory(400),
                                            beforeRemove: slideBeforeRemoveFactory(400)
                                        } -->
                                        <div>
                                            <div class="form-group">
                                                <label class="form-label">
                                                    Отображение процесса
                                                </label>
                                                <div class="f-btn-group mb-1" data-bind="
                                                        let: {
                                                            value: activeTheme().show_process
                                                        },
                                                    ">
                                                    <button class="f-btn" type="button" data-bind="
                                                            css: {
                                                                'active': value() == 1,
                                                            },
                                                            click: function() {
                                                                value(1);
                                                            },
                                                            disable: blocked,
                                                        ">
                                                        Номера страниц
                                                    </button>
                                                    <button class="f-btn" type="button" data-bind="
                                                            css: {
                                                                'active': value() == 2,
                                                            },
                                                            click: function() {
                                                                value(2);
                                                            },
                                                            disable: blocked,
                                                        ">
                                                        Прогресс-бар
                                                    </button>
                                                    <button class="f-btn" type="button" data-bind="
                                                            css: {
                                                                'active': value() == 0,
                                                            },
                                                            click: function() {
                                                                value(0);
                                                            },
                                                            disable: blocked,
                                                        ">
                                                        Ничего
                                                    </button>
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="col">
                                                    <div class="form-group">
                                                        <input-checkbox params="checked: activeTheme().disable_question_autoscroll, disabled: blocked || !pagesMode">
                                                            Отключить автоматическую прокрутку вопросов на странице
                                                            <svg class="ml-5p" width="7" height="18" viewBox="0 0 7 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                                <path d="M1 7.50873C1 8.89129 2.11929 10.0121 3.5 10.0121C4.88071 10.0121 6 8.89129 6 7.50873C6 6.12616 4.88071 5.00537 3.5 5.00537C4.36261 4.78305 5 3.93586 5 3.00269C5 1.89663 4.10457 1 3 1C1.89543 1 1 1.89663 1 3.00269V7.50873ZM1 7.50873V12" stroke="#37A74A" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                                                            </svg>

                                                            <question-button params="text: 'Автоматическая прокрутка вопросов при прохождении опроса доступна, когда на странице отображается больше одного вопроса'"></question-button>
                                                        </input-checkbox>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <!-- /ko -->
                                    </section>

                                    <div class="row">
                                        <div class="col col-6">
                                            <div class="form-group">
                                                <fc-label params="text: 'Метка необязательного вопроса', hint: 'Метка необязательного вопроса'"></fc-label>

                                                <fc-input params="value: activeTheme().unrequiredText, maxlength: 40, disabled: blocked"></fc-input>
                                                <div class="service-text mt-10p">Если метка не нужна, оставьте поле пустым</div>
                                            </div>
                                        </div>
                                    </div>



                                </div>

                                <!-- ko ifnot: blocked -->
                                <footer class="f-card__footer" data-bind="stickyFooter">
                                    <div class="settings__actions">
                                        <!-- ko ifnot: blocked -->

                                        <div>
                                            <button class="f-btn f-btn-link" type="button" data-bind="click: function() { setDefaultTheme(); }">Сбросить настройки (стандартная тема)</button>
                                        </div>

                                        <!-- /ko -->

                                        <div class="spacer"></div>

                                        <button class="f-btn" type="button" data-bind="click: function() {clearThemes();}">
                                            <span class="f-btn-prepend">
                                                <svg-icon params="name: 'bin'"></svg-icon>
                                            </span>
                                            Отменить
                                        </button>

                                        <?php if (!$isEditor) : ?>
                                            <button class="f-btn f-btn-success" type="button" data-bind="click: function () { openCreateModal(activeTheme().templateId); }">
                                                <span class="f-btn-prepend">
                                                    <svg-icon params="name: 'save'"></svg-icon>
                                                </span>
                                                Сохранить как новую тему
                                            </button>
                                        <?php endif; ?>


                                        <button class="f-btn f-btn-success" type="button" data-bind="click: function() {animateSaveBlock();}">
                                            <span class="f-btn-prepend">
                                                <svg-icon params="name: 'save'"></svg-icon>
                                            </span>
                                            Сохранить
                                        </button>

                                    </div>
                                    <success-message params="show: showSuccessMessage"></success-message>
                                </footer>
                                <!-- /ko -->
                            </div>

                            <div class="pt-15p pr-5p pl-5p flex-shrink-0 f-card f-card--shadow f-card--lg f-card--min-height poll-preview-wrapper" style="width: 370px;">
                                <div class="">
                                    <?php
                                    // Определяем схему протокола на основе хоста
                                    $host = $_SERVER['HTTP_HOST'];

                                    /**
                                     * @var string Схема протокола для URL
                                     * Если хост содержит localhost или .local, то используется http, иначе https
                                     */
                                    $isLocal = strpos($host, 'localhost') !== false || strpos($host, '.local') !== false;

                                    $scheme = $isLocal ? 'http' : 'https';

                                    // Проверка на режим старого превью
                                    $isOldPreview = isset($_GET['old-preview']) && $_GET['old-preview'] === '1';

                                    // URL для прохождения на Vue (теперь по умолчанию)
                                    // TODO сделать переменную окружения
                                    $previewUrl = $isLocal
                                        ? Url::to('http://localhost:5173/poll-vue/preview?pollId='.$poll->id.'&designPreview=1')
                                        : Url::to(['/poll-vue/preview', 'pollId' => $poll->id, 'designPreview' => 1], $scheme);

                                    // URL для прохождения на Vue в режиме торговой точки
                                    $tradePreviewUrl = $isLocal
                                        ? 'http://localhost:5173/poll-vue/preview/t/?pollId='.$poll->id.'&designPreview=1'
                                        : Url::to(['/poll-vue/preview/t/', 'pollId' => $poll->id, 'designPreview' => 1], $scheme);

                                    
                                    // URL для старого превью
                                    $oldPreviewUrl = Url::to(['/foquz/default/poll', 'authKey' => 'dummyDesign', 'questionId' => $model->id ?? '', 'pollId' => $poll->id], $scheme);
                                    $oldTradePreviewUrl = Url::to(['/foquz/default/poll', 'authKey' => 'dummyDesign', 'questionId' => $model->id ?? '', 'pollId' => $poll->id, 'tablet' => 1], $scheme);
                                    ?>
                                    <poll-preview params="
                                        title: pollName,
                                        isAuto: <?= $poll->is_auto ? 1 : 0 ?>,
                                        subtitle: 'Дизайн',
                                        mode: 'design',
                                        onChange: function() { $root.changeActiveIframe() },
                                        url: '<?= $isOldPreview ? $oldPreviewUrl : $previewUrl ?>',
                                        tradeUrl: '<?= $tradePreviewUrl ?>',
                                        isVuePreview: <?= !$isOldPreview ? 1 : 0 ?>,
                                    "></poll-preview>
                                </div>

                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- ko component: { name: 'modal-container', params: { opens: modalOpens } } -->
        <!-- /ko -->

        <script type="text/html" id="poll-design-delete-modal-content-template">
            <div data-bind="component: { name: 'poll-design-delete-modal-dialog', params: { data, modalElement, cancel: function () { close(); }, submit: function () { close(true); } } }" role="document"></div>
        </script>

        <script type="text/html" id="poll-design-rename-modal-content-template">
            <div class="poll-design__rename-modal-dialog-wrapper">
                <div class="poll-design__rename-modal-dialog-wrapper-actions">
                    <button class="btn poll-design__rename-modal-dialog-wrapper-close-button" data-bind="click: function () { close(); }"></button>
                </div>

                <div data-bind="component: { name: 'poll-design__rename-modal-dialog', params: { data, modalElement, cancel: function () { close(); }, submit: function (data) { close(data); } } }" role="document"></div>
            </div>
        </script>

        <!-- /ko -->
    </div>

</div>


<template id="poll-design-rename-modal-dialog-template">
    <!-- ko template: { afterRender: onInit } -->
    <div class="modal-content">
        <div class="modal-header">
            <h2 class="modal-title" data-bind="text: mode === 'edit'?'Переименовать тему':'Новая тема'">Переименовать тему</h2>

            <button type="button" class="close" aria-label="Close" data-bind="click: function() { cancel(); }">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>

        <div class="modal-body">

            <div class="form-group always-have-margin">
                <label class="d-flex align-items-center justify-content-between form-label">
                    <span>
                        Название
                        <button class="btn-question" data-bind="tooltip, tooltipPlacement: 'top'" type="button" title="Название темы">
                        </button>
                    </span>
                </label>
                <div class="chars-counter chars-counter--type_input" data-bind="charsCounter, charsCounterCount: title().length">
                    <input required class="form-control" name="title" data-bind="textInput: title,css: {
                                                           'is-invalid': $component.formControlLengthErrorStateMatcher(title),
                                                           'is-valid': $component.formControlSuccessStateMatcher(title)
                                                       }" maxlength="140" minlength="2">

                    <div class="chars-counter__value"></div>
                </div>
                <!-- ko if: $component.formControlLengthErrorStateMatcher(title) -->
                <div class="form-error" data-bind="text: $component.formControlLengthErrorStateMatcher(title)"></div>
                <!-- /ko -->
            </div>

        </div>

        <div class="modal-footer">
            <div class="modal-actions">
                <button type="button" class="btn btn-link" data-bind="click: function() { cancel(); }">
                    Отменить
                </button>

                <button type="submit" class="btn btn-default" data-bind="click: function() { submit(); }">
                    Сохранить
                </button>
            </div>
        </div>
    </div>
    <!-- /ko -->
</template>

<template id="theme-preview-template">
    <section class="poll-design__themes-theme">
        <div class="poll-design__themes-preview" data-bind="style: {'background-color': bgColor(), 'background-image': 'url('+themeImage()+')'}">

            <span class="poll-design__pseudo" data-bind="click: function(event) {$parent.setActiveTheme(id);}"></span>


            <div class="poll-design__themes-font" data-bind="style: {'font-family': fontFamilyStatic(),'color':colorStatic()}">
                <strong>Aa</strong> Aa
            </div>

            <?php if (!$isEditor) : ?>
                <!-- ko if: ( !isLocked) -->
                <div class="poll-design__themes-control dropdown survey-list__element-card-dropdown">

                    <a href="#" class="poll-design__themes-control poll-design__menu-button" data-toggle="dropdown" data-flip="false"></a>

                    <div class="dropdown-menu survey-list__element-card-dropdown-menu">
                        <a class="dropdown-item" data-bind="click: function () { $parent.openRenameModal(id); }">Переименовать</a>
                        <a class="dropdown-item" data-bind="click: function () { $parent.openDeleteModal(id); }">Удалить</a>
                    </div>

                </div>
                <!-- /ko -->
            <?php endif; ?>

        </div>
        <div class="poll-design__themes-name mb-0" data-bind="text: title,attr: {'data-original-title':title},click: function(event) {$parent.setActiveTheme(id);},tooltip, tooltipPlacement: 'bottom'," title=""></div>
    </section>
</template>

<!-- При наличии превью на странице, кнопка "Вверх" должна быть выше всех элементов -->
<style>
    .upward-button-container {
        z-index: 9999;
    }
</style>
