stages:
  - prebuild
  - build
  - deploy
  - bridge
  - test
  - update_snapshots
 
variables:
  TOKEN_TESTS_SCREENSHOTS: $screenshots_tests
  TAG: latest

# сборка dev
build-prerelease:
  stage: build
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH || $CI_PIPELINE_SOURCE == 'api' || $CI_COMMIT_BRANCH =~ /TASK-4756.*/
  image:
    name: node:20-alpine
  artifacts:
    paths:
      - prerelease/
  script:
    - export NODE_ENV=staging
    - npm install
    - npm run lint
    - npm run build -- --mode staging
    - mv dist prerelease

# сборка dev
build-prerelease-manual:
  stage: build
  when: manual
  tags:
    - docker-vps-tw
  image:
    name: node:20-alpine
  artifacts:
    paths:
      - prerelease/
  script:
    - export NODE_ENV=staging
    - npm install
    - npm run lint
    - npm run build -- --mode staging
    - mv dist prerelease    

# сборка для прода. ручной запуск
build-production:
  stage: build
  when: manual
  tags:
    - k8s
  image:
    name: node:20-alpine
  artifacts:
    paths:
      - production/
  script:
    - export NODE_ENV=production
    - npm install
    - npm run build:production
    - mv dist production

# сборка для прода. ручной запуск
build-production-r-ulybka:
  stage: build
  when: manual
  tags:
    - k8s
  image:
    name: node:20-alpine
  artifacts:
    paths:
      - production/
  script:
    - npm install
    - NODE_ENV=production VITE_ENABLE_ULIBKA_RADUGI_THEME=true npm run build -- --mode production 
    - mv dist production

# сборка для прода без sentry
build-production-no-sentry:
  stage: build
  when: manual
  tags:
    - k8s
  image:
    name: node:20-alpine
  artifacts:
    paths:
      - production/
  script:
    - export NODE_ENV=production
    - export VITE_USE_SENTRY=0
    - npm install
    - npm run build:production
    - mv dist production

# Джоба для запуска тестов
# В процессе запуска тестов происходит сравнение с текущими скриншотами
# Если добавляются новые скриншоты, то они автоматически коммитятся ботом в репозиторий
run_playwright_tests:
  stage: test
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH || $CI_PIPELINE_SOURCE == 'api'
  tags:
    - docker-vps-tw
  dependencies:
    - build-prerelease
  image: mcr.microsoft.com/playwright:v1.48.0-jammy
  allow_failure: true
  script:
    - npm ci

    - |
      set +e
      npx playwright test
      PLAYWRIGHT_EXIT_CODE=$?
      set -e
      if [ $PLAYWRIGHT_EXIT_CODE -ne 0 ]; then
        echo "Playwright tests failed with exit code $PLAYWRIGHT_EXIT_CODE"
      fi
    - |
      if [ -n "$(git status --porcelain tests/e2e/snapshots/)" ]; then
        git config --global user.email "<EMAIL>"
        git config --global user.name "CI Bot"
        git add tests/e2e/snapshots/
        git commit -m "Add new screenshots from CI Bot"
        git push https://screenshots_tests:${TOKEN_TESTS_SCREENSHOTS}@doxsw.gitlab.yandexcloud.net/doxsw/foquz-quiz.git HEAD:$CI_COMMIT_REF_NAME
        echo "New screenshots committed and pushed."
      else
        echo "No new screenshots to commit."
      fi
  artifacts:
    when: always
    paths:
      - tests/e2e/playwright-report/
      - tests/e2e/snapshots/
      - test-results/
    expire_in: 30 days

# Джоба для обновления скриншотов
# Иногда приходится обновлять скриншоты, если были изменения в дизайне
# Данная джоба запускается вручную и обновляет скриншоты
update_snapshots:
  stage: update_snapshots
  tags:
    - docker-vps-tw
  image:
    name: mcr.microsoft.com/playwright:v1.48.0-jammy
  dependencies:
    - build-prerelease
  script:
    - npm ci
    - npx playwright test --update-snapshots
    - git config --global user.email "<EMAIL>"
    - git config --global user.name "CI Bot"
    - git add --all
    - git commit -m "Update snapshots from CI Bot"
    - git push https://screenshots_tests:${TOKEN_TESTS_SCREENSHOTS}@doxsw.gitlab.yandexcloud.net/doxsw/foquz-quiz.git HEAD:$CI_COMMIT_REF_NAME
  when: manual
  allow_failure: true
  artifacts:
    when: always
    paths:
      - tests/e2e/playwright-report/
      - tests/e2e/snapshots/
      - test-results/
    expire_in: 30 days


include:
  - 'images/build-images-ci.yml'
