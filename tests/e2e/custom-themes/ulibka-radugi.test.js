import { expect, test } from '@playwright/test'
import { drag } from '../testUtils'

// Common setup function
async function setupTest(page, device = 'desktop') {
  await page.clock.install({ time: new Date('2025-03-06T08:00:00') })

  if (device === 'mobile') {
    await page.setViewportSize({ width: 375, height: 667 })
  }

  await page.goto('ulibka-radugi')
  await page.waitForTimeout(300)
}

// Helper function to take screenshot
async function takeScreenshot(page, name, device = 'desktop') {
  const devicePrefix = device === 'mobile' ? 'mobile/' : ''
  await expect(page).toHaveScreenshot(`custom-themes/ulibka-radugi/${devicePrefix}${name}.png`)
}

test.describe.skip('Ulibka Radugi Theme Tests', () => {
  test.describe('Start and End Screens', () => {
    test('should display and navigate from start screen', async ({ page }) => {
      await setupTest(page)

      // Вопрос 1: Промежуточный блок (ID: 177644)
      const startButton = page.getByText('Пройти опрос')
      await expect(startButton).toBeVisible()
      await takeScreenshot(page, 'start-screen-initial')

      await startButton.click()

      // Verify we moved to the first question (NPS Rating)
      const firstQuestion = page.locator('[data-question-id="177643"]')
      await expect(firstQuestion).toBeVisible()
    })

    test('should display end screen correctly', async ({ page }) => {
      await setupTest(page)

      // Skip to the last question (classifier)
      const questions = ['177643', '177651', '177657', '177658', '178074', '178075', '178076', '178077', '178078', '178079', '178080', '178081', '178082', '178085', '178086', '178087', '178088', '178089', '178090', '178091', '178092', '178093', '178094', '178095', '178096']

      await page.getByText('Пройти опрос').click()

      // Click through all questions to reach the end
      for (const id of questions) {
        await expect(page.locator(`[data-question-id="${id}"]`)).toBeVisible()

        const isFirstQuestion = id === questions[0]
        const isLastQuestion = id === questions[questions.length - 1]

        if (isFirstQuestion) {
          await page.locator('[data-question-id="177643"]').getByTestId('rating-nps-item').nth(5).click()
        }
        if (isLastQuestion)
          break

        await page.getByText('Далее').click()
      }

      await page.getByText('Завершить').click()

      // Вопрос 27: Промежуточный блок (конечный экран) (ID: 177653)
      const endScreen = page.locator('[data-question-id="177653"]')
      await expect(endScreen).toBeVisible()
      await takeScreenshot(page, 'end-screen-initial')
    })
  })

  test.describe('NPS Rating Questions', () => {
    test('should handle standard NPS rating', async ({ page }) => {
      await setupTest(page)

      // Skip start screen
      await page.getByText('Пройти опрос').click()

      // Вопрос 2: NPS Рейтинг (стандартный) (ID: 177643)
      const npsQuestion = page.locator('[data-question-id="177643"]')
      await expect(npsQuestion).toBeVisible()
      await takeScreenshot(page, 'nps-standard-unfilled')

      // Try to proceed without rating
      await page.getByText('Далее').click()
      await expect(page.getByText('Нужно поставить оценку')).toBeVisible()
      await takeScreenshot(page, 'nps-standard-error')

      // Set rating and proceed
      await npsQuestion.getByTestId('rating-nps-item').nth(5).click()
      await takeScreenshot(page, 'nps-standard-filled')
      await page.getByText('Далее').click()
    })

    test('should handle NPS with variants', async ({ page }) => {
      await setupTest(page)

      // Skip to NPS with variants question
      await page.getByText('Пройти опрос').click()
      await page.locator('[data-question-id="177643"]').getByTestId('rating-nps-item').nth(5).click()
      await page.getByText('Далее').click()

      // Вопрос 3: NPS Рейтинг (с вариантами) (ID: 177651)
      const npsVariants = page.locator('[data-question-id="177651"]')
      await expect(npsVariants).toBeVisible()
      await takeScreenshot(page, 'nps-variants-unfilled')

      const variants = npsVariants.getByTestId('rating-nps')
      for (let i = 0; i < await variants.count(); i++) {
        await variants.nth(i).getByTestId('rating-nps-item').nth(3).click()
      }

      // scroll to the bottom
      await page.evaluate(() => {
        window.scrollTo(0, document.body.scrollHeight)
      })

      await takeScreenshot(page, 'nps-variants-filled')
      await page.getByText('Далее').click()
    })
  })

  test.describe('Star Rating Questions', () => {
    test('should handle star rating questions', async ({ page }) => {
      await setupTest(page)

      // Skip to star rating question
      await page.getByText('Пройти опрос').click()
      await page.locator('[data-question-id="177643"]').getByTestId('rating-nps-item').nth(5).click()
      for (let i = 0; i < 4; i++) {
        await page.getByText('Далее').click()
      }

      // Вопрос 6: Звездный рейтинг (ID: 178074)
      const starQuestion = page.locator('[data-question-id="178074"]')
      await expect(starQuestion).toBeVisible()
      await takeScreenshot(page, 'star-rating-unfilled')

      await starQuestion.getByTestId('star-rating-item').nth(2).click() // Выбор 3-й звезды
      await takeScreenshot(page, 'star-rating-filled')
      await page.getByText('Далее').click()
    })
  })

  test.describe('Text and Date Questions', () => {
    test('should handle text input questions', async ({ page }) => {
      await setupTest(page)

      // Skip to text question
      await page.getByText('Пройти опрос').click()
      await page.locator('[data-question-id="177643"]').getByTestId('rating-nps-item').nth(5).click()
      for (let i = 0; i < 7; i++) {
        await page.getByText('Далее').click()
      }

      // Вопрос 9: Текстовый вопрос (ID: 178077)
      const textQuestion = page.locator('[data-question-id="178077"]')
      await expect(textQuestion).toBeVisible()
      await takeScreenshot(page, 'text-question-unfilled')

      await textQuestion.locator('input').fill('Пример текстового ответа')
      await takeScreenshot(page, 'text-question-filled')
      await page.getByText('Далее').click()
    })

    test('should handle date input questions', async ({ page }) => {
      await setupTest(page)

      // Skip to date question
      await page.getByText('Пройти опрос').click()
      await page.locator('[data-question-id="177643"]').getByTestId('rating-nps-item').nth(5).click()
      for (let i = 0; i < 8; i++) {
        await page.getByText('Далее').click()
      }

      // Вопрос 10: Вопрос с датой (ID: 178078)
      const dateQuestion = page.locator('[data-question-id="178078"]')
      await expect(dateQuestion).toBeVisible()
      await takeScreenshot(page, 'date-question-unfilled')

      await dateQuestion.getByTestId('datepicker-input').fill('01.01.2024')
      await dateQuestion.getByTestId('datepicker-input').blur()

      await takeScreenshot(page, 'date-question-filled')
      await page.getByText('Далее').click()
    })
  })

  test.describe('Scale Rating Questions', () => {
    test('should handle scale rating questions', async ({ page }) => {
      await setupTest(page)

      // Skip to scale rating question
      await page.getByText('Пройти опрос').click()
      await page.locator('[data-question-id="177643"]').getByTestId('rating-nps-item').nth(5).click()
      for (let i = 0; i < 5; i++) {
        await page.getByText('Далее').click()
      }

      // Вопрос 7: Шкала рейтинга (тип 18) (ID: 178075)
      const scaleQuestion = page.locator('[data-question-id="178075"]')
      await expect(scaleQuestion).toBeVisible()
      await takeScreenshot(page, 'scale-rating-unfilled')

      await scaleQuestion.getByTestId('rating-scale-item').nth(3).click()
      await takeScreenshot(page, 'scale-rating-filled')
      await page.getByText('Далее').click()
    })
  })

  test.describe('Multiple Choice Questions', () => {
    test('should handle multiple choice questions', async ({ page }) => {
      await setupTest(page)

      // Skip to multiple choice question
      await page.getByText('Пройти опрос').click()
      await page.locator('[data-question-id="177643"]').getByTestId('rating-nps-item').nth(5).click()
      for (let i = 0; i < 6; i++) {
        await page.getByText('Далее').click()
      }

      // Вопрос 8: Варианты (Множественный выбор) (ID: 178076)
      const multipleChoice = page.locator('[data-question-id="178076"]')
      await expect(multipleChoice).toBeVisible()
      await takeScreenshot(page, 'multiple-choice-unfilled')

      const checks = multipleChoice.getByTestId('variants-check')
      await checks.nth(0).click()
      await checks.nth(1).click()
      await checks.last().click()

      await multipleChoice.locator('textarea').first().fill('Текстовый комментарий')
      await takeScreenshot(page, 'multiple-choice-filled')
      await page.getByText('Далее').click()
    })
  })

  test.describe('Quiz Form Questions', () => {
    test('should handle quiz form questions', async ({ page }) => {
      await setupTest(page)

      // Skip to quiz question
      await page.getByText('Пройти опрос').click()
      await page.locator('[data-question-id="177643"]').getByTestId('rating-nps-item').nth(5).click()
      for (let i = 0; i < 11; i++) {
        await page.getByText('Далее').click()
      }

      // Вопрос 13: Вопрос-анкета (ID: 178081)
      const quizQuestion = page.locator('[data-question-id="178081"]')
      await expect(quizQuestion).toBeVisible()
      await takeScreenshot(page, 'quiz-form-unfilled')

      // Fill all quiz fields
      await quizQuestion.getByTestId('quiz-question-nomask-field').first().fill('Текст формы')
      await quizQuestion.getByTestId('quiz-question-phone-field').fill('9991234567')
      await quizQuestion.getByTestId('quiz-question-email-field').fill('<EMAIL>')
      await quizQuestion.getByTestId('quiz-question-number-field').fill('42')
      await quizQuestion.getByTestId('quiz-question-site-field').fill('https://example.com')
      await quizQuestion.getByTestId('quiz-question-surname-field').fill('Иванов')
      await quizQuestion.getByTestId('quiz-question-name-field').fill('Иван')
      await quizQuestion.getByTestId('quiz-question-patronymic-field').fill('Иванович')

      const dateField = quizQuestion.getByTestId('quiz-question-date-field')
      await dateField.fill('10.03.2025')
      await dateField.blur()

      await page.evaluate(() => window.scrollTo(0, 0))
      await takeScreenshot(page, 'quiz-form-filled-top')

      await page.evaluate(() => window.scrollTo(0, document.body.scrollHeight))

      await page.waitForTimeout(300)

      await takeScreenshot(page, 'quiz-form-filled-bottom')
      await page.getByText('Далее').click()
    })
  })

  test.describe('Priority Questions', () => {
    test('should handle priority sorting questions', async ({ page }) => {
      await setupTest(page)

      // Skip to priority question
      await page.getByText('Пройти опрос').click()
      await page.locator('[data-question-id="177643"]').getByTestId('rating-nps-item').nth(5).click()
      for (let i = 0; i < 12; i++) {
        await page.getByText('Далее').click()
      }

      // Вопрос 14: Вопрос с приоритетом (ID: 178082)
      const priorityQuestion = page.locator('[data-question-id="178082"]')
      await expect(priorityQuestion).toBeVisible()
      await takeScreenshot(page, 'priority-question-unfilled')

      const items = priorityQuestion.locator('.priority-item')
      await drag(page, items.first(), items.nth(1))
      await takeScreenshot(page, 'priority-question-filled')
      await page.getByText('Далее').click()
    })
  })

  test.describe('Scale Questions', () => {
    test('should handle scale input questions', async ({ page }) => {
      await setupTest(page)

      // Skip to scale question
      await page.getByText('Пройти опрос').click()
      await page.locator('[data-question-id="177643"]').getByTestId('rating-nps-item').nth(5).click()
      for (let i = 0; i < 13; i++) {
        await page.getByText('Далее').click()
      }

      // Вопрос 15: Вопрос со шкалой (ID: 178085)
      const scaleQuestion = page.locator('[data-question-id="178085"]')
      await expect(scaleQuestion).toBeVisible()
      await takeScreenshot(page, 'scale-input-unfilled')

      await scaleQuestion.locator('.fc-input__field').fill('50')
      await scaleQuestion.locator('.fc-input__field').blur()
      await takeScreenshot(page, 'scale-input-filled')
      await page.getByText('Далее').click()
    })

    test('should handle scale with variants', async ({ page }) => {
      await setupTest(page)

      // Skip to scale variants question
      await page.getByText('Пройти опрос').click()
      await page.locator('[data-question-id="177643"]').getByTestId('rating-nps-item').nth(5).click()
      for (let i = 0; i < 14; i++) {
        await page.getByText('Далее').click()
      }

      // Вопрос 16: Вопрос со шкалой (с вариантами) (ID: 178086)
      const scaleVariants = page.locator('[data-question-id="178086"]')
      await expect(scaleVariants).toBeVisible()
      await takeScreenshot(page, 'scale-variants-unfilled')

      const scales = scaleVariants.locator('.fc-input__field')
      for (const scale of await scales.all()) {
        await scale.fill('60')
        await scale.blur()
      }
      await takeScreenshot(page, 'scale-variants-filled')
      await page.getByText('Далее').click()
    })
  })

  test.describe('Media Questions', () => {
    test('should handle media selection questions', async ({ page }) => {
      await setupTest(page)

      // Skip to media question
      await page.getByText('Пройти опрос').click()
      await page.locator('[data-question-id="177643"]').getByTestId('rating-nps-item').nth(5).click()
      for (let i = 0; i < 15; i++) {
        await page.getByText('Далее').click()
      }

      // Вопрос 17: Вопрос с выбором медиа (ID: 178087)
      const mediaQuestion = page.locator('[data-question-id="178087"]')
      await expect(mediaQuestion).toBeVisible()
      await takeScreenshot(page, 'media-selection-unfilled')

      await mediaQuestion.locator('.gallery-item__select-button').first().click()
      await takeScreenshot(page, 'media-selection-filled')
      await page.getByText('Далее').click()
    })

    test('should handle gallery rating questions', async ({ page }) => {
      await setupTest(page)

      // Skip to gallery rating question
      await page.getByText('Пройти опрос').click()
      await page.locator('[data-question-id="177643"]').getByTestId('rating-nps-item').nth(5).click()
      for (let i = 0; i < 16; i++) {
        await page.getByText('Далее').click()
      }

      // Вопрос 18: Вопрос с рейтингом галереи (ID: 178088)
      const galleryQuestion = page.locator('[data-question-id="178088"]')
      await expect(galleryQuestion).toBeVisible()
      await takeScreenshot(page, 'gallery-rating-unfilled')

      const items = galleryQuestion.locator('.gallery-item')
      for (const item of await items.all()) {
        await item.getByTestId('star-rating-item').nth(2).click()
      }

      await page.evaluate(() => window.scrollTo(0, 0))
      await takeScreenshot(page, 'gallery-rating-filled')
      await page.getByText('Далее').click()
    })
  })

  test.describe('Matrix Questions', () => {
    test('should handle matrix questions', async ({ page }) => {
      await setupTest(page)

      // Skip to matrix question
      await page.getByText('Пройти опрос').click()
      await page.locator('[data-question-id="177643"]').getByTestId('rating-nps-item').nth(5).click()
      for (let i = 0; i < 18; i++) {
        await page.getByText('Далее').click()
      }

      // Вопрос 20: Матричный вопрос (ID: 178090)
      const matrixQuestion = page.locator('[data-question-id="178090"]')
      await expect(matrixQuestion).toBeVisible()
      await takeScreenshot(page, 'matrix-unfilled')

      const rows = matrixQuestion.locator('.matrix-question__row')
      for (let i = 0; i < await rows.count(); i++) {
        await rows.nth(i).locator('.matrix-question__cell').nth(2).click()
      }
      await takeScreenshot(page, 'matrix-filled')
      await page.getByText('Далее').click()
    })

    test('should handle matrix with dropdown questions', async ({ page }) => {
      await setupTest(page)

      // Skip to matrix dropdown question
      await page.getByText('Пройти опрос').click()
      await page.locator('[data-question-id="177643"]').getByTestId('rating-nps-item').nth(5).click()
      for (let i = 0; i < 19; i++) {
        await page.getByText('Далее').click()
      }

      // Вопрос 21: Матричный вопрос (с выпадающим списком) (ID: 178091)
      const matrixDropdown = page.locator('[data-question-id="178091"]')
      await expect(matrixDropdown).toBeVisible()
      await takeScreenshot(page, 'matrix-dropdown-unfilled')

      const triggers = matrixDropdown.locator('.matrix-question .select-trigger')
      for (let i = 0; i < await triggers.count(); i++) {
        await triggers.nth(i).click()
        await page.locator('.command-item').first().click()
      }
      await takeScreenshot(page, 'matrix-dropdown-filled')
      await page.getByText('Далее').click()
    })
  })
  test('should show intermediate block', async ({ page }) => {
    await setupTest(page)
    await page.getByText('Пройти опрос').click()
    await page.locator('[data-question-id="177643"]').getByTestId('rating-nps-item').nth(5).click()
    for (let i = 0; i < 23; i++) {
      await page.getByText('Далее').click()
      await page.waitForTimeout(300)
    }

    // Вопрос 22: Промежуточный блок (ID: 178095)
    const intermediateBlock = page.locator('[data-question-id="178095"]')
    await expect(intermediateBlock).toBeVisible()
    await takeScreenshot(page, 'intermediate-block-initial')
  })

  test.describe('Mobile View Tests', () => {
    test('should handle date picker on mobile', async ({ page }) => {
      await setupTest(page, 'mobile')

      // Skip to date question
      await page.getByText('Пройти опрос').click()
      await page.locator('[data-question-id="177643"]').getByTestId('rating-nps-item').nth(5).click()
      for (let i = 0; i < 8; i++) {
        await page.getByText('Далее').click()
      }

      // Вопрос 10: Вопрос с датой (ID: 178078)
      const dateQuestion = page.locator('[data-question-id="178078"]')
      const dateInput = dateQuestion.getByTestId('datepicker-input')

      await expect(dateQuestion).toBeVisible()
      await takeScreenshot(page, 'date-question-mobile-unfilled', 'mobile')

      await dateInput.click()
      await page.locator('.CalendarCellTrigger[day="2025-03-10"]').first().click()
      await page.locator('.close-button--close').click()

      await takeScreenshot(page, 'date-question-mobile-filled', 'mobile')
      await page.getByText('Далее').click()
    })
  })
})
