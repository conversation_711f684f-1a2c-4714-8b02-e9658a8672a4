import { expect, test } from '@playwright/test'

// Helper function to check if pagination numbers are inactive
async function expectPaginationNumbersToBeInactive(page, numbers) {
  for (const number of numbers) {
    const paginatorItem = page.locator(`.fc-paginator-item[data-blocked="false"][data-active="false"][data-visible="false"] .fc-paginator-item__index:text-is("${number}")`)
    await expect(paginatorItem).toBeVisible()
  }
}

test.describe('Логические переходы: Несколько вопросов на странице', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('logic-multiple-questions-per-page')
  })

  test.describe('Логика первого звездного рейтинга (ЗР 1)', () => {
    test('Пропускает страницу 2 если у "ЗР 1" выбрано 5 звезд', async ({ page }) => {
      // Get all star rating groups on the page
      const starRatings = page.getByTestId('star-rating')

      // Select 5 stars for the first rating (ЗР 1)
      await starRatings.first().getByTestId('star-rating-item').last().click()

      // Fill other required ratings with any value
      await starRatings.nth(1).getByTestId('star-rating-item').first().click() // ЗР 2
      await starRatings.nth(2).getByTestId('star-rating-item').first().click() // ЗР 3

      await page.waitForTimeout(100)
      await page.getByText('Далее').click()

      // Verify we skipped page 2 and are on page 3
      await expect(page.getByText('ЗР 7')).toBeVisible()
      await expect(page.getByText('ЗР 5')).not.toBeVisible()

      // Verify pagination numbers are correct
      await expectPaginationNumbersToBeInactive(page, [2])
    })

    test('Проходит на страницу 2 если у "ЗР 1" не выбрано 5 звезд', async ({ page }) => {
      const starRatings = page.getByTestId('star-rating')

      // Select 3 stars for the first rating (ЗР 1)
      await starRatings.first().getByTestId('star-rating-item').nth(2).click()

      // Fill other required ratings with any value
      await starRatings.nth(1).getByTestId('star-rating-item').first().click() // ЗР 2
      await starRatings.nth(2).getByTestId('star-rating-item').first().click() // ЗР 3

      await page.getByText('Далее').click()

      // Verify we're on page 2
      await expect(page.getByText('ЗР 5')).toBeVisible()

      // Verify no pagination numbers are inactive
      await expectPaginationNumbersToBeInactive(page, [])
    })
  })

  test.describe('Логика второго звездного рейтинга (ЗР 2)', () => {
    test('Пропускает страницу 2 и 3 если у "ЗР 2" выбрано 5 звезд', async ({ page }) => {
      const starRatings = page.getByTestId('star-rating')

      // Select 3 stars for ЗР 1 (to not trigger its logic)
      await starRatings.first().getByTestId('star-rating-item').nth(2).click()

      // Select 5 stars for ЗР 2
      await starRatings.nth(1).getByTestId('star-rating-item').last().click()

      // Fill other required ratings
      await starRatings.nth(2).getByTestId('star-rating-item').first().click() // ЗР 3

      await page.getByText('Далее').click()

      // Verify we skipped pages 2 and 3 and are on the end screen
      await expect(page.getByText('ЗР 5')).not.toBeVisible()
      await expect(page.getByText('ЗР 7')).not.toBeVisible()

      await expect(page.getByText('ЗР 8')).toBeVisible()
      await expect(page.getByText('ЗР 9')).toBeVisible()

      // Verify pagination numbers are correct
      await expectPaginationNumbersToBeInactive(page, [2, 3])
    })

    test('Проходит на страницу 2 если у "ЗР 2" не выбрано 5 звезд', async ({ page }) => {
      const starRatings = page.getByTestId('star-rating')

      // Select 3 stars for ЗР 1 (to not trigger its logic)
      await starRatings.first().getByTestId('star-rating-item').nth(2).click()

      // Select 3 stars for ЗР 2
      await starRatings.nth(1).getByTestId('star-rating-item').nth(2).click()

      // Fill other required ratings
      await starRatings.nth(2).getByTestId('star-rating-item').first().click() // ЗР 3

      await page.getByText('Далее').click()

      // Verify we're on page 2
      await expect(page.getByText('ЗР 5')).toBeVisible()

      // Verify no pagination numbers are inactive
      await expectPaginationNumbersToBeInactive(page, [])
    })
  })

  test('Корректно возвращается к предыдущим вопросам', async ({ page }) => {
    const starRatings = page.getByTestId('star-rating')

    // Fill first page ratings
    await starRatings.first().getByTestId('star-rating-item').nth(2).click() // ЗР 1
    await starRatings.nth(1).getByTestId('star-rating-item').nth(2).click() // ЗР 2
    await starRatings.nth(2).getByTestId('star-rating-item').first().click() // ЗР 3

    await page.getByText('Далее').click()

    // Fill second page ratings
    const page2Ratings = page.getByTestId('star-rating')
    await page2Ratings.first().getByTestId('star-rating-item').first().click() // ЗР 5
    await page2Ratings.nth(1).getByTestId('star-rating-item').first().click() // ЗР 6

    await page.getByText('Далее').click()

    // Go back to second page
    await page.getByText('Вернуться').click()

    // Verify we're back on second page and ratings are preserved
    await expect(page.getByText('ЗР 5')).toBeVisible()
    await expect(page2Ratings.first().getByTestId('star-rating-item').first()).toHaveClass(/selected/gi)

    // Verify no pagination numbers are inactive
    await expectPaginationNumbersToBeInactive(page, [])
  })
})
