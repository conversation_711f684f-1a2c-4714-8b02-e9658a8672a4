/**
 * @file Содержит тесты для логических переходов с вопросами:
 * - Однострочное поле
 * - Дата/время
 * - Анкета
 * - Приоритет
 */

import { expect, test } from '@playwright/test'

// Helper function to check if pagination numbers are inactive
async function expectPaginationNumbersToBeInactive(page, numbers) {
  for (const number of numbers) {
    const paginatorItem = page.locator(`.fc-paginator-item[data-blocked="false"][data-active="false"][data-visible="false"] .fc-paginator-item__index:text-is("${number}")`)
    await expect(paginatorItem).toBeVisible()
  }
}

test.describe('Логические переходы: Часть 2', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('logic-part-2')
  })

  test.describe('Однострочное поле', () => {
    test('Переходит на вопрос "Дата/время" при любом ответе', async ({ page }) => {
      // Enter any text in single line field
      const input = page.locator('input[type="text"]')
      await input.fill('Test answer')
      await page.getByText('Далее').click()

      // Verify we're on Date/Time question
      await expect(page.getByText('Дата/время')).toBeVisible()

      // Verify pagination numbers are inactive
      await expectPaginationNumbersToBeInactive(page, [2])
    })
  })

  test.describe('Дата/время', () => {
    test('Переходит на вопрос "Анкета" при любом ответе', async ({ page }) => {
      // Navigate to Date/Time question
      const input = page.locator('input[type="text"]')
      await input.fill('Test answer')
      await page.getByText('Далее').click()

      // Fill date field
      const dateInput = page.getByTestId('datepicker-input')
      await dateInput.fill('14.11.2024')
      await dateInput.press('Escape')
      await page.getByText('Далее').click()

      // Verify we're on Form question
      await expect(page.getByText('Анкета')).toBeVisible()

      // Verify pagination numbers are inactive
      await expectPaginationNumbersToBeInactive(page, [2, 4])
    })
  })

  test.describe('Анкета', () => {
    test('Переходит на вопрос "Приоритет" при заполнении обязательных полей', async ({ page }) => {
      // Navigate to Form question
      const input = page.locator('input[type="text"]')
      await input.fill('Test answer')
      await page.getByText('Далее').click()

      const dateInput = page.getByTestId('datepicker-input')
      await dateInput.fill('14.11.2024')
      await dateInput.press('Escape')
      await page.getByText('Далее').click()

      // Fill required form fields
      const phoneInput = page.locator('input[placeholder="+7 (___) ___ - ____"]')
      await phoneInput.fill('9991234567')

      const dateFormInput = page.getByTestId('quiz-question-date-field')
      await dateFormInput.fill('20.03.2024')
      await dateFormInput.press('Escape')

      await page.getByText('Далее').click()

      await expectPaginationNumbersToBeInactive(page, [2, 4, 6])

      // Verify we're on Priority question
      await expect(page.getByText('Приоритет')).toBeVisible()
    })
  })

  test.describe('Приоритет', () => {
    test('Завершает опрос при любом выборе', async ({ page }) => {
      // Navigate to Priority question
      const input = page.locator('input[type="text"]')
      await input.fill('Test answer')
      await page.getByText('Далее').click()

      const dateInput = page.getByTestId('datepicker-input')
      await dateInput.fill('14.11.2024')
      await dateInput.press('Escape')
      await page.getByText('Далее').click()

      const phoneInput = page.locator('input[placeholder="+7 (___) ___ - ____"]')
      await phoneInput.fill('9991234567')
      const dateFormInput = page.getByTestId('quiz-question-date-field')
      await dateFormInput.fill('20.03.2024')
      await dateFormInput.press('Escape')
      await page.getByText('Далее').click()

      // Select first priority option
      await page.getByText('вар 1').click()
      await page.getByText('Завершить').click()

      // Verify survey completion
      await expect(page.getByText('Опрос успешно пройден!')).toBeVisible()
    })
  })

  test('Корректно возвращается к предыдущим вопросам', async ({ page }) => {
    // Navigate through all questions
    const input = page.locator('input[type="text"]')
    await input.fill('Test answer')
    await page.getByText('Далее').click()

    const dateInput = page.getByTestId('datepicker-input')
    await dateInput.fill('14.11.2024')
    await dateInput.press('Escape')
    await page.getByText('Далее').click()

    const phoneInput = page.locator('input[placeholder="+7 (___) ___ - ____"]')
    await phoneInput.fill('9991234567')
    const dateFormInput = page.getByTestId('quiz-question-date-field')
    await dateFormInput.fill('20.03.2024')
    await dateFormInput.press('Escape')
    await page.getByText('Далее').click()

    // Now on Priority question, go back through each question
    await page.getByText('Вернуться').click()
    await expect(page.getByText('Анкета')).toBeVisible()
    await page.waitForTimeout(100)

    await page.getByText('Вернуться').click()
    await expect(page.getByText('Дата/время')).toBeVisible()
    await page.waitForTimeout(100)

    await page.getByText('Вернуться').click()
    await expect(page.locator('input[type="text"]').first()).toBeVisible()

    await expectPaginationNumbersToBeInactive(page, [2])
  })
})
