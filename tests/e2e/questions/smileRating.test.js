import { expect, test } from '@playwright/test'

test.describe('Вопрос \'smile rating\'', () => {
  test('Отображает 2 сердечка по умолчанию', async ({ page }) => {
    await page.goto('rating-smile-heart-2')
    const smiles = await page.getByTestId('smile-rating-item')
    await expect(smiles).toHaveCount(2)
    await page.waitForLoadState('networkidle')
    await expect(page).toHaveScreenshot('smile-rating-heart-2.png')

    await smiles.first().click()
    await expect(page).toHaveScreenshot('smile-rating-heart-2-selected.png')
  })

  test('Отображает 2 сердечка с метками', async ({ page }) => {
    await page.goto('rating-smile-heart-2-with-labels')
    const smile = await page.getByTestId('smile-rating-item').first()
    const label = await page.getByText('mark 1')

    await smile.click()
    await expect(label).toBeVisible()
    await page.waitForLoadState('networkidle')
    await expect(page).toHaveScreenshot(
      'smile-rating-heart-2-with-labels.png',
    )
  })

  test('Отображает 2 сердечка с опцией \'Всегда отображать метки\'', async ({
    page,
  }) => {
    await page.goto('rating-smile-heart-2-show-labels')
    const smiles = await page.getByTestId('smile-rating-item')
    await expect(smiles).toHaveCount(2)
    const labels = await page.getByTestId('smile-rating-item-label')
    await expect(labels).toHaveCount(2)
    await expect(labels).toHaveText(['mark 1', 'mark 2'])
    await page.waitForLoadState('networkidle')
    await expect(page).toHaveScreenshot(
      'smile-rating-heart-2-show-labels.png',
    )
  })

  test('Отображает 2 лайка по умолчанию', async ({ page }) => {
    await page.goto('rating-smile-like-2')
    const smiles = await page.getByTestId('smile-rating-item')
    await expect(smiles).toHaveCount(2)
    await page.waitForLoadState('networkidle')
    await expect(page).toHaveScreenshot('smile-rating-like-2.png')
  })

  test('Отображает 2 лайка с метками', async ({ page }) => {
    await page.goto('rating-smile-like-2-with-labels')
    const smile = await page.getByTestId('smile-rating-item').first()
    const label = await page.getByText('mark 1')
    await smile.click()
    await expect(label).toBeVisible()
    await page.waitForLoadState('networkidle')
    await expect(page).toHaveScreenshot(
      'smile-rating-like-2-with-labels.png',
    )
  })

  test('Отображает 2 лайка с опцией \'Всегда отображать метки\'', async ({
    page,
  }) => {
    await page.goto('rating-smile-like-2-show-labels')
    const smiles = await page.getByTestId('smile-rating-item')
    await expect(smiles).toHaveCount(2)
    const labels = await page.getByTestId('smile-rating-item-label')
    await expect(labels).toHaveCount(2)
    await expect(labels).toHaveText(['mark 1', 'mark 2'])
    await page.waitForLoadState('networkidle')
    await expect(page).toHaveScreenshot(
      'smile-rating-like-2-show-labels.png',
    )
  })

  test('Отображает 3 лица по умолчанию', async ({ page }) => {
    await page.goto('rating-smile-face-3')
    const smiles = await page.getByTestId('smile-rating-item')
    await expect(smiles).toHaveCount(3)
    await page.waitForLoadState('networkidle')
    await expect(page).toHaveScreenshot('smile-rating-face-3.png')
    await smiles.first().click()
    await expect(page).toHaveScreenshot('smile-rating-face-3-selected.png')
  })

  test('Отображает 3 лица с метками', async ({ page }) => {
    await page.goto('rating-smile-face-3-with-labels')
    const smile = await page.getByTestId('smile-rating-item').nth(1)
    const label = await page.getByText('mark 3')

    await smile.click()
    await expect(label).toBeVisible()
    await page.waitForLoadState('networkidle')
    await expect(page).toHaveScreenshot(
      'smile-rating-face-3-with-labels.png',
    )
  })

  test('Отображает 3 лица с опцией \'Всегда отображать метки\'', async ({
    page,
  }) => {
    await page.goto('rating-smile-face-3-show-labels')
    const smiles = await page.getByTestId('smile-rating-item')
    await expect(smiles).toHaveCount(3)
    const labels = await page.getByTestId('smile-rating-item-label')
    await expect(labels).toHaveCount(3)
    await expect(labels).toHaveText(['mark 1', 'mark 3', 'mark 5'])
    await page.waitForLoadState('networkidle')
    await expect(page).toHaveScreenshot(
      'smile-rating-face-3-show-labels.png',
    )
  })

  test('Отображает 5 роботов по умолчанию', async ({ page }) => {
    await page.goto('rating-smile-robot-5')
    const smiles = await page.getByTestId('smile-rating-item')
    await expect(smiles).toHaveCount(5)
    await page.waitForLoadState('networkidle')
    await expect(page).toHaveScreenshot('smile-rating-robot-5.png')
    await smiles.first().click()
    await expect(page).toHaveScreenshot('smile-rating-robot-5-selected.png')
  })

  test('Отображает 5 роботов с метками', async ({ page }) => {
    await page.goto('rating-smile-robot-5-with-labels')
    const smile = await page.getByTestId('smile-rating-item').nth(1)
    const label = await page.getByText('mark 2')

    await smile.click()
    await expect(label).toBeVisible()
    await page.waitForLoadState('networkidle')
    await expect(page).toHaveScreenshot(
      'smile-rating-robot-5-with-labels.png',
    )
  })

  test('Отображает 5 роботов с опцией \'Всегда отображать метки\'', async ({
    page,
  }) => {
    await page.goto('rating-smile-robot-5-show-labels')
    const smiles = await page.getByTestId('smile-rating-item')
    await expect(smiles).toHaveCount(5)
    const labels = await page.getByTestId('smile-rating-item-label')
    await expect(labels).toHaveCount(5)
    await expect(labels).toHaveText([
      'mark 1',
      'mark 2',
      'mark 3',
      'mark 4',
      'mark 5',
    ])
    await page.waitForLoadState('networkidle')
    await expect(page).toHaveScreenshot(
      'smile-rating-robot-5-show-labels.png',
    )
  })

  test('Отображает 3 желтых лица по умолчанию', async ({ page }) => {
    await page.goto('rating-smile-color_face-3')
    const smiles = await page.getByTestId('smile-rating-item')
    await expect(smiles).toHaveCount(3)
    await page.waitForLoadState('networkidle')
    await expect(page).toHaveScreenshot('smile-rating-color_face-3.png')
    await smiles.first().click()
    await expect(page).toHaveScreenshot(
      'smile-rating-color_face-3-selected.png',
    )
  })

  test('Отображает 3 желтых лица с метками', async ({ page }) => {
    await page.goto('rating-smile-color_face-3-with-labels')
    const smile = await page.getByTestId('smile-rating-item').nth(1)
    const label = await page.getByText('mark 3')

    await smile.click()
    await expect(label).toBeVisible()
    await page.waitForLoadState('networkidle')
    await expect(page).toHaveScreenshot(
      'smile-rating-color_face-3-with-labels.png',
    )
  })

  test('Отображает 3 желтых лица с опцией \'Всегда отображать метки\'', async ({
    page,
  }) => {
    await page.goto('rating-smile-color_face-3-show-labels')
    const smiles = await page.getByTestId('smile-rating-item')
    await expect(smiles).toHaveCount(3)
    const labels = await page.getByTestId('smile-rating-item-label')
    await expect(labels).toHaveCount(3)
    await expect(labels).toHaveText(['mark 1', 'mark 3', 'mark 5'])
    await page.waitForLoadState('networkidle')
    await expect(page).toHaveScreenshot(
      'smile-rating-color_face-3-show-labels.png',
    )
  })

  test('Отображает 5 погодных иконок по умолчанию', async ({ page }) => {
    await page.goto('rating-smile-weather-5')
    const smiles = await page.getByTestId('smile-rating-item')
    await expect(smiles).toHaveCount(5)
    await page.waitForLoadState('networkidle')
    await expect(page).toHaveScreenshot('smile-rating-weather-5.png')
    await smiles.first().click()
    await expect(page).toHaveScreenshot(
      'smile-rating-weather-5-selected.png',
    )
  })

  test('Отображает 5 погодных иконок с метками', async ({ page }) => {
    await page.goto('rating-smile-weather-5-with-labels')
    const smile = await page.getByTestId('smile-rating-item').nth(1)
    const label = await page.getByText('mark 2')

    await smile.click()
    await expect(label).toBeVisible()
    await page.waitForLoadState('networkidle')
    await expect(page).toHaveScreenshot(
      'smile-rating-weather-5-with-labels.png',
    )
  })

  test('Отображает 5 погодных иконок с опцией \'Всегда отображать метки\'', async ({
    page,
  }) => {
    await page.goto('rating-smile-weather-5-show-labels')
    const smiles = await page.getByTestId('smile-rating-item')
    await expect(smiles).toHaveCount(5)
    const labels = await page.getByTestId('smile-rating-item-label')
    await expect(labels).toHaveCount(5)
    await expect(labels).toHaveText([
      'mark 1',
      'mark 2',
      'mark 3',
      'mark 4',
      'mark 5',
    ])
    await page.waitForLoadState('networkidle')
    await expect(page).toHaveScreenshot(
      'smile-rating-weather-5-show-labels.png',
    )
  })

  test('Отображает 5 разных иконок по умолчанию', async ({ page }) => {
    await page.goto('rating-smile-emoji-5')
    const smiles = await page.getByTestId('smile-rating-item')
    await expect(smiles).toHaveCount(5)
    await page.waitForLoadState('networkidle')
    await expect(page).toHaveScreenshot('smile-rating-emoji-5.png')
    await smiles.nth(2).click()
    await expect(page).toHaveScreenshot('smile-rating-emoji-5-selected.png')
  })

  test('Отображает 5 разных иконок с метками', async ({ page }) => {
    await page.goto('rating-smile-emoji-5-with-labels')
    const smile = await page.getByTestId('smile-rating-item').nth(1)
    const label = await page.getByText('mark 2')

    await smile.click()
    await expect(label).toBeVisible()
    await page.waitForLoadState('networkidle')
    await expect(page).toHaveScreenshot(
      'smile-rating-emoji-5-with-labels.png',
    )
  })

  test('Отображает 5 разных иконок с опцией \'Всегда отображать метки\'', async ({
    page,
  }) => {
    await page.goto('rating-smile-emoji-5-show-labels')
    const smiles = await page.getByTestId('smile-rating-item')
    await expect(smiles).toHaveCount(5)
    const labels = await page.getByTestId('smile-rating-item-label')
    await expect(labels).toHaveCount(5)
    await expect(labels).toHaveText([
      'mark 1',
      'mark 2',
      'mark 3',
      'mark 4',
      'mark 5',
    ])
    await page.waitForLoadState('networkidle')
    await expect(page).toHaveScreenshot(
      'smile-rating-emoji-5-show-labels.png',
    )
  })
  test('Отображает чекбокс \'Пропустить вопрос\' если опция включена', async ({
    page,
  }) => {
    await page.goto('rating-smile-with-skip')
    const skipCheckbox = await page.getByText('Пропустить вопрос')
    await expect(skipCheckbox).toBeVisible()
    await page.waitForLoadState('networkidle')
    await expect(page).toHaveScreenshot('smile-rating-with-skip.png')
  })

  test('Пропускает обязательный вопрос если опция \'Пропустить вопрос\' включена', async ({
    page,
  }) => {
    await page.goto('rating-smile-with-skip-required')
    const skipCheckbox = await page.getByText('Пропустить вопрос')
    const finishButton = await page.getByText('Далее')
    const error = await page.getByText('Нужно поставить оценку')

    await page.waitForLoadState('networkidle')

    await finishButton.click()
    await expect(error).toBeVisible()

    await page.waitForTimeout(300)
    await expect(page).toHaveScreenshot(
      'smile-rating-with-skip-required-error.png',
    )

    await skipCheckbox.click()
    await finishButton.click()
    await expect(error).not.toBeVisible()
  })

  test('Скрывает комментарий если опция \'Пропустить вопрос\' включена', async ({
    page,
  }) => {
    await page.goto('rating-smile-with-skip-and-comment')
    const skipCheckbox = await page.getByText('Пропустить вопрос')
    const comment = await page.getByTestId('comment')

    await expect(comment).toBeVisible()
    await page.waitForLoadState('networkidle')
    await expect(page).toHaveScreenshot(
      'smile-rating-with-skip-and-comment.png',
    )
    await skipCheckbox.click()
    await expect(comment).not.toBeVisible()
    await page.waitForLoadState('networkidle')
    await expect(page).toHaveScreenshot(
      'smile-rating-with-skip-and-comment-hidden.png',
    )
    await skipCheckbox.click()
    await expect(comment).toBeVisible()
    await expect(page).toHaveScreenshot(
      'smile-rating-with-skip-and-comment.png',
    )
  })
  // Tests for rating with comment
  test.describe('Комментарий', () => {
    test('Отображает комментарий', async ({ page }) => {
      await page.goto('rating-smile-with-comment')
      const comment = await page.getByTestId('comment')
      const label = comment.getByText('Ваш комментарий')

      await expect(comment).toBeVisible()
      await expect(label).toBeVisible()

      await page.waitForLoadState('networkidle')
      await expect(page).toHaveScreenshot('smile-rating-with-comment.png')
    })

    test('Не обязателен по умолчанию', async ({ page }) => {
      await page.goto('rating-smile-with-comment')
      const smiles = await page.getByTestId('smile-rating')
      const smile = await page.getByTestId('smile-rating-item').first()
      const finishButton = await page.getByText('Далее')

      await smile.click()
      await finishButton.click()

      // Проверяем, что перешли на следующий шаг
      await expect(smiles).toBeHidden()
    })

    test('Обязателен если опция \'Обязателен\' включена', async ({ page }) => {
      await page.goto('rating-smile-with-comment-required')
      const smiles = await page.getByTestId('smile-rating')
      const smile = await page.getByTestId('smile-rating-item').first()
      const finishButton = await page.getByText('Далее')
      const error = await page.getByText('Обязательное поле')
      const textarea = await page.getByTestId('comment').locator('textarea')

      await page.waitForLoadState('networkidle')
      await expect(error).toBeHidden()
      await finishButton.click()
      await page.waitForTimeout(300)
      await expect(error).toBeVisible()
      await expect(page).toHaveScreenshot(
        'smile-rating-with-comment-required-error.png',
      )

      await smile.click()
      await textarea.fill('some value')

      // Проверяем, что ошибки нет и перешли на следующий шаг
      await expect(error).toBeHidden()
      await finishButton.click()
      await expect(smiles).toBeHidden()
    })

    test('Комплексный пример (Обязателен + валидация длины текста)', async ({
      page,
    }) => {
      await page.goto('rating-smile-with-comment-complex')
      const smiles = await page.getByTestId('smile-rating')
      const smile = await page.getByTestId('smile-rating-item').first()
      const finishButton = await page.getByText('Далее')
      const requiredError = await page.getByText('Обязательное поле')
      const minLengthError = await page.getByText(
        'Должно быть введено хотя бы 10 символов',
      )
      const textarea = await page.getByTestId('comment').locator('textarea')

      await expect(requiredError).toBeHidden()
      await finishButton.click()
      await expect(requiredError).toBeVisible()
      await page.waitForLoadState('networkidle')
      await expect(page).toHaveScreenshot(
        'smile-rating-with-comment-complex-error.png',
      )

      await smile.click()
      await textarea.fill('text')
      await expect(minLengthError).toBeVisible()
      await expect(page).toHaveScreenshot(
        'smile-rating-with-comment-complex-minlength-error.png',
      )
      await textarea.fill('text with more than 10 characters')

      await expect(minLengthError).toBeHidden()
      await expect(requiredError).toBeHidden()
      await expect(page).toHaveScreenshot(
        'smile-rating-with-comment-complex-no-error.png',
      )

      // Проверяем, что ошибки нет и перешли на следующий шаг
      await finishButton.click()
      await expect(smiles).toBeHidden()
    })
  })
})
