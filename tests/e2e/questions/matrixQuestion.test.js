import { expect, test } from '@playwright/test'

test.describe('Вопрос "матрица"', () => {
  test('Базовый матричный вопрос', async ({ page }) => {
    await page.goto('matrix-basic')
    const nextButton = await page.getByText('Далее')
    const matrixRows = await page.locator('.matrix-question__row')
    const error = await page.getByText('Необходимо ответить хотя бы на 2')
    // const errors = await page.getByText('Нужно выбрать один из вариантов').locator('visible=true')

    await expect(matrixRows).toHaveCount(3)

    await expect(page).toHaveScreenshot('matrix-basic-initial.png')
    await nextButton.click()
    await expect(error).toBeVisible()

    await page.evaluate(() => window.scrollTo(0, 0))
    await expect(page).toHaveScreenshot('matrix-basic-required.png')

    for (let i = 0; i < 3; i++) {
      await matrixRows.nth(i).locator('.matrix-question__cell').first().click()
    }

    await expect(error).not.toBeVisible()
    await page.evaluate(() => window.scrollTo(0, 0))

    // Проверяем уточняющий вопрос
    const questionVariants = await page.locator('.question-variants')
    await expect(questionVariants).toHaveCount(2)

    const checkboxes = await questionVariants.first().locator('.fc-check')
    await expect(checkboxes).toHaveCount(4) // 3 причины + "Другое"

    await checkboxes.nth(0).click()

    await questionVariants.last().locator('.fc-check').last().click()
    await page.locator('textarea').fill('Тестовый комментарий')

    await expect(page).toHaveScreenshot('matrix-basic-filled.png')

    // Проверяем, что нажатие на кнопку "Далее" приводит к переходу на следующий вопрос
    await nextButton.click()
    await expect(matrixRows.first()).not.toBeVisible()
  })

  test('Матричный вопрос с множественным выбором', async ({ page }) => {
    await page.goto('matrix-multiple-choice')
    const matrixRows = await page.locator('.matrix-question__row')

    await matrixRows.first().locator('.matrix-question__cell').nth(0).click()
    await matrixRows.first().locator('.matrix-question__cell').nth(1).click()

    await expect(page).toHaveScreenshot('matrix-multiple-choice.png')
  })

  test('Матричный вопрос с возможностью пропуска', async ({ page }) => {
    await page.goto('matrix-with-skip')
    const skipButton = await page.getByText('Пропустить этот вопрос')
    const matrixRows = await page.locator('.matrix-question__row')
    const nextButton = await page.getByText('Далее')

    await expect(skipButton).toBeVisible()
    await skipButton.click()

    await expect(nextButton).toBeVisible()
    await page.evaluate(() => window.scrollTo(0, 0))
    await expect(page).toHaveScreenshot('matrix-with-skip-skipped.png')
    await nextButton.click()

    await expect(matrixRows.first()).not.toBeVisible()
  })

  test('Матричный вопрос с рандомизацией вариантов', async ({ page }) => {
    await page.goto('matrix-randomized')
    const matrixRows = await page.locator('.matrix-question__row')

    // Здесь мы не можем проверить сам факт рандомизации, но можем убедиться, что все варианты на месте
    for (const row of await matrixRows.all()) {
      const cellTexts = await row.locator('.matrix-question__cell-text').allInnerTexts()
      expect(cellTexts).toEqual(expect.arrayContaining(['Плохо', 'Удовлетворительно', 'Хорошо', 'Отлично']))
    }
  })

  test('Матричный вопрос с обязательным комментарием', async ({ page }) => {
    await page.goto('matrix-with-required-comment')
    const nextButton = await page.getByText('Далее')
    const matrixRows = await page.locator('.matrix-question__row')
    const commentField = await page.locator('textarea')

    await expect(commentField).toBeVisible()
    await expect(page).toHaveScreenshot('matrix-with-required-comment-initial.png')

    for (let i = 0; i < 3; i++) {
      await matrixRows.nth(i).locator('.matrix-question__cell').first().click()
    }

    await nextButton.click()
    await expect(page.getByText('Обязательное поле')).toBeVisible()

    await page.evaluate(() => window.scrollTo(0, 0))
    await expect(page).toHaveScreenshot('matrix-with-required-comment-required.png')

    await commentField.fill('Тестовый комментарий')

    await page.evaluate(() => window.scrollTo(0, 0))
    await expect(page).toHaveScreenshot('matrix-with-required-comment-filled.png')
    await nextButton.click()

    await expect(matrixRows.first()).not.toBeVisible()
  })

  test('Матричный вопрос без уточняющего вопроса', async ({ page }) => {
    await page.goto('matrix-without-extra-question')
    const matrixRows = await page.locator('.matrix-question__row')
    const questionVariants = await page.locator('.question-variants')

    for (let i = 0; i < 3; i++) {
      await matrixRows.nth(i).locator('.matrix-question__cell').first().click()
    }

    await expect(questionVariants).toHaveCount(0)
  })

  test('Матричный вопрос на английском языке', async ({ page }) => {
    await page.goto('matrix-english?lang=en')
    const matrixRows = await page.locator('.matrix-question__row')
    const rowTitles = await matrixRows.locator('.matrix-question__row-title')
    const cellTexts = await matrixRows.first().locator('.matrix-question__cell-text')

    expect(rowTitles).toContainText(['Service 1', 'Service 2', 'Service 3'])
    expect(cellTexts).toContainText(['Poor', 'Fair', 'Good', 'Excellent'])

    await expect(page.getByText('Please rate our services')).toBeVisible()

    // click first cell
    await matrixRows.first().locator('.matrix-question__cell').first().click()

    await expect(page.getByText('Why did you give this rating?')).toBeVisible()

    // check for variants in English
    const variants = await page.locator('.question-variants').locator('.fc-check')
    await expect(variants).toContainText(['Reason 1', 'Reason 2', 'Reason 3'])
  })

  test('Матричный вопрос с выпадающими вариантами', async ({ page }) => {
    await page.goto('matrix-with-dropdown-variants')
    const nextButton = await page.getByText('Далее')
    const matrixRows = await page.locator('.matrix-question__row')
    const error = await page.getByText('Необходимо ответить хотя бы на 2')
    const triggers = await page.locator('.matrix-question .select-trigger')

    await expect(triggers).toHaveCount(3)

    await expect(page).toHaveScreenshot('matrix-with-dropdown-variants-initial.png')
    await nextButton.click()
    await expect(error).toBeVisible()

    await page.evaluate(() => window.scrollTo(0, 0))
    await expect(page).toHaveScreenshot('matrix-with-dropdown-variants-required.png')

    for (let i = 0; i < 3; i++) {
      await triggers.nth(i).click()
      await page.locator('.command-item').first().click()
    }

    await expect(error).not.toBeVisible()

    // // Проверяем уточняющий вопрос
    const questionVariants = await page.locator('.question-variants')
    await expect(questionVariants).toHaveCount(2)

    const checkboxes = await questionVariants.first().locator('.fc-check')
    await expect(checkboxes).toHaveCount(4) // 3 причины + "Другое"

    await checkboxes.nth(0).click()

    await questionVariants.last().locator('.fc-check').last().click()
    await page.locator('textarea').fill('Тестовый комментарий')

    await page.waitForTimeout(1000)
    await page.evaluate(() => window.scrollTo(0, 0))
    await expect(page).toHaveScreenshot('matrix-with-dropdown-variants-filled.png')

    // Проверяем, что нажатие на кнопку "Далее" приводит к переходу на следующий вопрос
    await nextButton.click()
    await expect(matrixRows.first()).not.toBeVisible()
  })
})
