import { expect, test } from '@playwright/test'

test.describe('Вопрос "Промежуточный блок"', () => {
  test.describe('Стартовый экран', () => {
    test('Отображает стартовый экран с кнопкой начала опроса', async ({ page }) => {
      await page.goto('intermediate-block-start')
      const startButton = await page.getByText('Пройти опрос')
      const text = await page.locator('.text')
      const promocodeButton = await page.locator('.copy-promocode__button')
      const languageSelectTrigger = await page.locator('.text .select-trigger')

      await expect(text).toBeVisible()
      await expect(startButton).toBeVisible()
      await expect(page).toHaveScreenshot('intermediate-block-start.png')

      await promocodeButton.click()
      await page.waitForTimeout(300)
      await expect(page).toHaveScreenshot('intermediate-block-start-promocode-copied.png')

      await languageSelectTrigger.click()

      await page.waitForTimeout(300)
      await page.evaluate(() => window.scrollTo(0, 0))
      await expect(page).toHaveScreenshot('intermediate-block-start-language-select-opened.png')
    })

    test('Отображает чекбокс согласия если agreement включен', async ({ page }) => {
      await page.goto('intermediate-block-start')
      const agreement = await page.locator('.survey-interscreen__agreement')
      const startButton = await page.getByText('Пройти опрос')

      await expect(agreement).toBeVisible()
      await expect(startButton).toBeDisabled()
      await expect(page).toHaveScreenshot('intermediate-block-start-agreement-initial.png')

      const checkbox = await page.locator('.survey-interscreen__agreement .fc-check')
      await checkbox.click()

      // Переходим на следующий экран
      await startButton.click()
      await expect(agreement).not.toBeVisible()
    })
  })

  test.describe('Промежуточный экран', () => {
    test('Отображает промежуточный экран с переменными', async ({ page }) => {
      await page.goto('intermediate-block-default')
      const text = await page.locator('.text')

      await expect(text).toBeVisible()
      await expect(text).toContainText('Иван Иванов') // Проверяем подстановку переменной ФИО
      await expect(text).toContainText('PROMO123') // Проверяем подстановку промокода
      await expect(page).toHaveScreenshot('intermediate-block-default.png')
    })

    test('Отображает логотипы с корректными размерами', async ({ page }) => {
      await page.goto('intermediate-block-default')
      const images = await page.locator('.interscreen-image')

      await expect(images).toHaveCount(3)
      const firstImage = images.first()

      await firstImage.scrollIntoViewIfNeeded()
      await expect(firstImage).toHaveAttribute('style', /width: 133px/)
      await expect(page).toHaveScreenshot('intermediate-block-default-logos.png')
    })
  })

  test.describe('Конечный экран', () => {
    test('Отображает конечный экран с кнопкой готово', async ({ page }) => {
      await page.goto('intermediate-block-end')
      const readyButton = await page.getByText('Готово')

      await expect(readyButton).toBeVisible()
      await expect(readyButton).toHaveAttribute('href', 'https://example.com/external-link')
      await expect(page).toHaveScreenshot('intermediate-block-end.png')

      // scroll to the end of the page
      await page.evaluate(() => {
        window.scrollTo(0, document.body.scrollHeight)
      })

      await expect(page).toHaveScreenshot('intermediate-block-end-scrolled.png')
    })
  })
})
