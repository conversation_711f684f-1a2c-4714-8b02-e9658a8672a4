import { expect, test } from '@playwright/test'
import { waitForUIStability } from '../testUtils'

test.describe('Вопрос \'Дата\'', () => {
  test.describe('Дата', () => {
    test('Отображает дату по умолчанию', async ({ page }) => {
      await page.clock.install({ time: new Date('2024-09-06T08:00:00') })
      await page.goto('date-default')
      const dateInput = await page.getByTestId('datepicker-input')
      const nextButton = await page.getByText('Далее')
      const requiredError = await page.getByText('Обязательное поле')
      // const errorFormat = await page.getByText('Неверный формат')

      await waitForUIStability(page)
      await expect(dateInput).toBeVisible()

      await expect(page).toHaveScreenshot('date-default.png')

      await nextButton.click()
      await expect(requiredError).toBeVisible()

      await expect(page).toHaveScreenshot('date-required-error.png')

      await dateInput.fill('02.06.2024')
      await dateInput.press('Escape')
      await dateInput.press('Escape')

      await expect(page).toHaveScreenshot('date-required-filled.png')
    })

    test('Отображает ошибку при неверном формате', async ({ page }) => {
      await page.goto('date-default')
      const dateInput = await page.getByTestId('datepicker-input')
      const nextButton = await page.getByText('Далее')
      const requiredError = await page.getByText('Обязательное поле')
      const errorFormat = await page.getByText('Неверный формат')

      await waitForUIStability(page)
      await expect(dateInput).toBeVisible()

      await nextButton.click()
      await expect(requiredError).toBeVisible()

      await dateInput.fill('02.06.20')

      await expect(errorFormat).toBeVisible()
    })

    test('Необязательное поле: Все равно отображает ошибку "Неверный формат"', async ({ page }) => {
      await page.goto('date-default-unrequired')
      const dateInput = await page.getByTestId('datepicker-input')
      const nextButton = await page.getByText('Далее')
      const errorFormat = await page.getByText('Неверный формат')

      await waitForUIStability(page)
      await expect(dateInput).toBeVisible()

      await dateInput.fill('02.06.20')
      await dateInput.press('Escape')
      await dateInput.press('Escape')

      await nextButton.click()

      await expect(errorFormat).toBeVisible()

      await dateInput.clear()
      await dateInput.press('Escape')
      await dateInput.press('Escape')

      // Переходим на следующий вопрос
      await nextButton.click()
      await expect(errorFormat).not.toBeVisible()
      await expect(dateInput).not.toBeVisible()
    })
    test('Позволяет выбрать год и месяц из выпадающих списков в календаре', async ({ page }) => {
      await page.clock.install({ time: new Date('2024-09-06T08:00:00') })
      await page.goto('date-default')
      const dateInput = await page.getByTestId('datepicker-input')
      const monthSelect = await page.locator('.Calendar__MonthSelect')
      const yearSelect = await page.locator('.Calendar__YearSelect')

      await dateInput.focus()
      await page.waitForTimeout(300)

      // Select month
      await monthSelect.click()

      await page.waitForTimeout(100)
      await expect(page).toHaveScreenshot('date-month-select-opened.png')

      await page.getByText('Март').scrollIntoViewIfNeeded()
      await page.getByText('Март').click()
      await page.waitForTimeout(300)

      await expect(page).toHaveScreenshot('date-month-select-selected.png')

      // Select year
      await yearSelect.click()

      await expect(page).toHaveScreenshot('date-year-select-opened.png')

      await page.getByText('2025').click()
      await page.waitForTimeout(300)

      await expect(page).toHaveScreenshot('date-year-select-selected.png')

      // click on the date cell
      const dateCell = page.locator('.CalendarCellTrigger').nth(10)
      await dateCell.click()
      await page.waitForTimeout(300)

      await expect(page).toHaveScreenshot('date-month-year-selected.png')
    })
    test('Выбор года и месяца на мобильном устройстве', async ({ page }) => {
      await page.clock.install({ time: new Date('2024-09-06T08:00:00') })
      await page.goto('date-default')
      await page.setViewportSize({ width: 375, height: 700 })
      const dateInput = await page.getByTestId('datepicker-input')
      const monthSelect = await page.locator('.Calendar__MonthSelect')
      const yearSelect = await page.locator('.Calendar__YearSelect')

      await dateInput.focus()
      await page.waitForTimeout(300)

      await monthSelect.click()

      await expect(page).toHaveScreenshot('date-mobile-month-opened.png')

      await page.getByText('Март').click()

      await page.waitForTimeout(300)

      await expect(page).toHaveScreenshot('date-mobile-month-selected-closed.png')

      await yearSelect.click()

      await expect(page).toHaveScreenshot('date-mobile-year-opened.png')

      await page.getByText('2025').click()

      await expect(page).toHaveScreenshot('date-mobile-year-selected-closed.png')
    })
  })

  test.describe('День и месяц', () => {
    test('Отображает день и месяц', async ({ page }) => {
      await page.goto('date-date-month')
      const nextButton = await page.getByText('Далее')
      const dayInput = await page.getByTestId('datemonth-day-input')
      const monthSelectTrigger = await page.locator('.date-month-mask-container .select-trigger')
      const requiredError = await page.getByText('Обязательное поле')

      await waitForUIStability(page)
      await expect(dayInput).toBeVisible()
      await expect(monthSelectTrigger).toBeVisible()

      await expect(page).toHaveScreenshot('date-month-day-default.png')

      await nextButton.click()
      await expect(requiredError).toBeVisible()

      await expect(page).toHaveScreenshot('date-month-day-required-error.png')

      await dayInput.fill('01')
      await expect(requiredError).not.toBeVisible()
    })

    test('Позволяет выбрать месяц из выпадающего списка', async ({ page }) => {
      await page.goto('date-date-month')
      const nextButton = await page.getByText('Далее')
      const monthSelectTrigger = await page.locator('.date-month-mask-container .select-trigger')
      const dayInput = await page.getByTestId('datemonth-day-input')
      const requiredError = await page.getByText('Обязательное поле')

      await waitForUIStability(page)
      await expect(monthSelectTrigger).toBeVisible()

      await monthSelectTrigger.click()
      await page.waitForTimeout(300)

      await expect(page).toHaveScreenshot('date-datemonth-select-opened.png')

      await page.getByText('Март').click()

      await expect(page).toHaveScreenshot('date-datemonth-select-selected.png')

      await dayInput.fill('01')
      await expect(requiredError).not.toBeVisible()

      // Переходим на следующий вопрос

      await nextButton.click()
      await expect(dayInput).not.toBeVisible()
      await expect(monthSelectTrigger).not.toBeVisible()
    })
    test('Необязательное поле: Все равно отображает ошибку если заполнено лишь одно из полей', async ({ page }) => {
      await page.goto('date-date-month-unrequired')
      const nextButton = await page.getByText('Далее')
      const dayInput = await page.getByTestId('datemonth-day-input')
      const monthSelectTrigger = await page.locator('.date-month-mask-container .select-trigger')
      const requiredError = await page.getByText('Обязательное поле')

      await waitForUIStability(page)
      await expect(dayInput).toBeVisible()
      await expect(monthSelectTrigger).toBeVisible()

      await dayInput.fill('01')

      await nextButton.click()
      await expect(requiredError).toBeVisible()

      await dayInput.clear()

      await expect(requiredError).not.toBeVisible()

      // Переходим на следующий вопрос
      await nextButton.click()
      await expect(dayInput).not.toBeVisible()
      await expect(monthSelectTrigger).not.toBeVisible()
    })
    test('Мобильное устройство: Отображает день и месяц', async ({ page }) => {
      await page.goto('date-date-month')
      await page.setViewportSize({ width: 375, height: 700 })
      const nextButton = await page.getByText('Далее')

      await expect(nextButton).toBeVisible()
      await expect(page).toHaveScreenshot('date-mobile-month-day-default.png')
    })
  })
  test.describe('Время', () => {
    test('Отображает время', async ({ page }) => {
      await page.goto('date-time')
      const nextButton = await page.getByText('Далее')
      const timeInput = await page.getByTestId('time-input')
      const requiredError = await page.getByText('Обязательное поле')

      await waitForUIStability(page)
      await expect(timeInput).toBeVisible()

      await expect(page).toHaveScreenshot('date-time-default.png')

      await nextButton.click()
      await expect(requiredError).toBeVisible()

      await expect(page).toHaveScreenshot('date-time-required-error.png')
    })
    test('Мобильное устройство: Отображает время', async ({ page }) => {
      await page.goto('date-time')
      await page.setViewportSize({ width: 375, height: 700 })
      const nextButton = await page.getByText('Далее')

      await expect(nextButton).toBeVisible()
      await expect(page).toHaveScreenshot('date-mobile-time-default.png')
    })

    test('Отображает ошибку при неверном формате', async ({ page }) => {
      await page.goto('date-time')
      const nextButton = await page.getByText('Далее')
      const timeInput = await page.getByTestId('time-input')
      const requiredError = await page.getByText('Обязательное поле')
      const errorFormat = await page.getByText('Некорректный формат')

      await waitForUIStability(page, 500)
      await expect(timeInput).toBeVisible()

      await timeInput.fill('2:')

      await nextButton.click()
      await expect(requiredError).not.toBeVisible()
      await expect(errorFormat).toBeVisible()

      await timeInput.clear()

      await expect(requiredError).toBeVisible()

      await timeInput.fill('22:00')

      await expect(requiredError).not.toBeVisible()
    })
    test('Необязательное поле: Все равно отображает ошибку если введено некорректное значение', async ({ page }) => {
      await page.goto('date-time-unrequired')
      const nextButton = await page.getByText('Далее')
      const timeInput = await page.getByTestId('time-input')
      const requiredError = await page.getByText('Обязательное поле')

      const errorFormat = await page.getByText('Некорректный формат')

      await waitForUIStability(page, 500)
      // Add a wait for navigation to complete
      await page.waitForTimeout(500)
      
      await expect(timeInput).toBeVisible()

      await timeInput.fill('2:')

      await nextButton.click()
      await expect(requiredError).not.toBeVisible()
      await expect(errorFormat).toBeVisible()

      await timeInput.clear()

      // Переходим на следующий вопрос
      await nextButton.click()
      await waitForUIStability(page)
      await expect(requiredError).not.toBeVisible()
      await expect(errorFormat).not.toBeVisible()
      await expect(timeInput).not.toBeVisible()
    })
  })
  test.describe('Дата и время', () => {
    test('Отображает дату и время', async ({ page }) => {
      await page.goto('date-date-time')
      const nextButton = await page.getByText('Далее')
      const dateInput = await page.getByTestId('datepicker-input')
      const timeInput = await page.getByTestId('time-input')
      const requiredErrorFirst = await page.getByText('Обязательное поле').first()
      const requiredErrorSecond = await page.getByText('Обязательное поле').nth(1)

      await waitForUIStability(page)
      await expect(dateInput).toBeVisible()
      await expect(timeInput).toBeVisible()

      await expect(page).toHaveScreenshot('date-datetime-default.png')

      await nextButton.click()
      await expect(requiredErrorFirst).toBeVisible()
      await expect(requiredErrorSecond).toBeVisible()

      await expect(page).toHaveScreenshot('date-datetime-required-error.png')

      await dateInput.fill('01.01.2024')
      await timeInput.fill('12:00')
      await expect(requiredErrorFirst).not.toBeVisible()
      await expect(requiredErrorSecond).not.toBeVisible()
    })
    test('Мобильное устройство: Отображает дату и время', async ({ page }) => {
      await page.goto('date-date-time')
      await page.setViewportSize({ width: 375, height: 700 })

      const nextButton = await page.getByText('Далее')
      await expect(nextButton).toBeVisible()
      await expect(page).toHaveScreenshot('date-mobile-datetime-default.png')
    })
    test('Необязательное поле: Все равно отображает ошибку если заполнено лишь одно из полей', async ({ page }) => {
      await page.goto('date-date-time-unrequired')
      const nextButton = await page.getByText('Далее')
      const dateInput = await page.getByTestId('datepicker-input')
      const timeInput = await page.getByTestId('time-input')
      const requiredError = await page.getByText('Обязательное поле')

      await waitForUIStability(page)
      await expect(dateInput).toBeVisible()
      await expect(timeInput).toBeVisible()

      await dateInput.fill('01.01.2024')

      await dateInput.press('Escape')
      await nextButton.click()
      await expect(requiredError).toBeVisible()

      await dateInput.clear()
      await dateInput.press('Escape')

      await expect(requiredError).not.toBeVisible()

      await page.waitForTimeout(300)
      // Переходим на следующий вопрос
      await nextButton.click()
      await expect(requiredError).not.toBeVisible()
      await expect(dateInput).not.toBeVisible()
    })
  })
  test.describe('День и месяц и время', () => {
    test('Отображает день, месяц и время', async ({ page }) => {
      await page.goto('date-date-month-time')
      const nextButton = await page.getByText('Далее')
      const dayInput = await page.getByTestId('datemonth-day-input')
      const monthSelectTrigger = await page.locator('.date-datemonthtime-mask-container .select-trigger')
      const timeInput = await page.getByTestId('time-input')
      const requiredErrorFirst = await page.getByText('Обязательное поле').first()
      const requiredErrorSecond = await page.getByText('Обязательное поле').nth(1)

      await waitForUIStability(page)
      await expect(dayInput).toBeVisible()
      await expect(monthSelectTrigger).toBeVisible()
      await expect(timeInput).toBeVisible()

      await expect(page).toHaveScreenshot('date-datemonth-time-default.png')

      await nextButton.click()
      await expect(requiredErrorFirst).toBeVisible()
      await expect(requiredErrorSecond).toBeVisible()

      await expect(page).toHaveScreenshot('date-datemonth-time-required-error.png')

      await dayInput.fill('01')
      await monthSelectTrigger.click()
      await page.getByText('Март').click()
      await timeInput.fill('12:00')
      await expect(requiredErrorFirst).not.toBeVisible()
      await expect(requiredErrorSecond).not.toBeVisible()

      // Переходим на следующий вопрос
      await nextButton.click()
      await expect(dayInput).not.toBeVisible()
      await expect(monthSelectTrigger).not.toBeVisible()
      await expect(timeInput).not.toBeVisible()
    })
    test('Мобильное устройство: Отображает день, месяц и время', async ({ page }) => {
      await page.goto('date-date-month-time')
      await page.setViewportSize({ width: 375, height: 700 })
      const nextButton = await page.getByText('Далее')

      await expect(nextButton).toBeVisible()
      await expect(page).toHaveScreenshot('date-mobile-datemonth-time-default.png')

      await nextButton.click()
      await expect(page).toHaveScreenshot('date-mobile-datemonth-time-required-error.png')
    })
    test('Необязательное поле: Все равно отображает ошибку если заполнено лишь одно из полей', async ({ page }) => {
      await page.goto('date-date-month-time-unrequired')
      const nextButton = await page.getByText('Далее')
      const dayInput = await page.getByTestId('datemonth-day-input')
      const monthSelectTrigger = await page.locator('.date-datemonthtime-mask-container .select-trigger')
      const monthSelectTriggerClear = await page.locator('.select-trigger__clear')
      const timeInput = await page.getByTestId('time-input')
      const requiredError = await page.getByText('Обязательное поле')

      await waitForUIStability(page)
      await expect(dayInput).toBeVisible()
      await expect(monthSelectTrigger).toBeVisible()
      await expect(timeInput).toBeVisible()

      await dayInput.fill('01')
      await monthSelectTrigger.click()
      await page.getByText('Март').click()

      await nextButton.click()
      await expect(requiredError).toBeVisible()

      await dayInput.clear()
      await monthSelectTriggerClear.click()

      await page.waitForTimeout(300)

      await expect(requiredError).not.toBeVisible()

      // Переходим на следующий вопрос
      await nextButton.click()
      await expect(dayInput).not.toBeVisible()
      await expect(monthSelectTrigger).not.toBeVisible()
      await expect(timeInput).not.toBeVisible()
    })
  })
})
