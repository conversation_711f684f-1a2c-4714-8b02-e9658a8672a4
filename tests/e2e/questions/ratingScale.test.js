import { expect, test } from '@playwright/test'

test.describe('Вопрос \'Рейтинг\'', () => {
  test('Отображает 5 цифр по умолчанию', async ({ page }) => {
    await page.goto('rating')
    const items = await page.getByTestId('rating-scale-item').locator('visible=true')
    await expect(items).toHaveCount(5)
    await expect(items).toHaveText(['1', '2', '3', '4', '5'])
    await expect(page).toHaveScreenshot('rating.png')
    await items.first().click()
    await expect(page).toHaveScreenshot('rating-clicked.png')
  })

  test('Отображает 2 цифры', async ({ page }) => {
    await page.goto('rating-2-items')
    const items = await page.getByTestId('rating-scale-item').locator('visible=true')
    await expect(items).toHaveText(['1', '2'])
    await expect(page).toHaveScreenshot('rating-2-items.png')
  })

  test('Отображает 10 цифр', async ({ page }) => {
    await page.goto('rating-10-items')
    const items = await page.getByTestId('rating-scale-item').locator('visible=true')
    await expect(items).toHaveText([
      '1',
      '2',
      '3',
      '4',
      '5',
      '6',
      '7',
      '8',
      '9',
      '10',
    ])
    await expect(page).toHaveScreenshot('rating-10-items.png')
  })

  test('Отображает метку при клике на цифру', async ({ page }) => {
    await page.goto('rating-with-labels')
    const items = await page.getByTestId('rating-scale-item').locator('visible=true')
    const labelOne = await page.getByText('метка 1')
    await items.first().click()
    await expect(labelOne).toBeVisible()
    await expect(page).toHaveScreenshot('rating-with-labels-clicked.png')
  })

  test('Обязателен по умолчанию', async ({ page }) => {
    await page.goto('rating')
    const item = await page.getByTestId('rating-scale-item').first()
    const error = await page.getByText('Нужно поставить оценку')
    const nextButton = await page.getByText('Далее')
    await nextButton.click()

    await expect(error).toBeVisible()
    await expect(page).toHaveScreenshot('rating-required-error.png')

    await item.click()
    await expect(error).not.toBeVisible()
    await expect(nextButton).not.toHaveClass(/disabled/)
  })

  test('Необязателен если опция \'Обязательный\' выключена', async ({ page }) => {
    await page.goto('rating-unrequired')
    const rating = await page.getByTestId('rating-scale').locator('visible=true')
    const nextButton = await page.getByText('Далее')
    const error = await page.getByText('Нужно поставить оценку')
    await expect(rating).toBeVisible()
    await nextButton.click()

    await expect(error).not.toBeVisible()
    await expect(rating).toBeVisible()
  })

  test('Отображает чекбокс \'Пропустить вопрос\' если опция включена', async ({ page }) => {
    await page.goto('rating-with-skip')
    const skipCheckbox = await page.getByText('Пропустить вопрос')
    await expect(skipCheckbox).toBeVisible()
    await expect(page).toHaveScreenshot('rating-with-skip.png')
  })

  test('Пропускает обязательный вопрос если опция \'Пропустить вопрос\' включена', async ({ page }) => {
    await page.goto('rating-with-skip-required')
    const skipCheckbox = await page.getByText('Пропустить вопрос')
    const nextButton = await page.getByText('Далее')
    const error = await page.getByText('Нужно поставить оценку')

    await nextButton.click()
    await expect(error).toBeVisible()
    await expect(page).toHaveScreenshot('rating-with-skip-required-error.png')

    await skipCheckbox.click()
    await nextButton.click()
    await expect(error).not.toBeVisible()
  })

  test('Скрывает комментарий если опция \'Пропустить вопрос\' включена', async ({ page }) => {
    await page.goto('rating-with-skip-and-comment')
    const skipCheckbox = await page.getByText('Пропустить вопрос')
    const comment = await page.getByTestId('comment').locator('visible=true')

    await expect(comment).toBeVisible()
    await expect(page).toHaveScreenshot('rating-with-skip-and-comment.png')
    await skipCheckbox.click()
    await expect(comment).not.toBeVisible()
    await expect(page).toHaveScreenshot('rating-with-skip-and-comment-hidden.png')
    await skipCheckbox.click()
    await expect(comment).toBeVisible()
    await expect(page).toHaveScreenshot('rating-with-skip-and-comment.png')
  })

  test.describe('Комментарий', () => {
    test('Отображает комментарий', async ({ page }) => {
      await page.goto('rating-with-comment')
      const comment = await page.getByTestId('comment').locator('visible=true')
      const label = comment.getByText('Ваш комментарий')

      await expect(comment).toBeVisible()
      await expect(label).toBeVisible()
      await expect(page).toHaveScreenshot('rating-with-comment.png')
    })

    test('Не обязателен по умолчанию', async ({ page }) => {
      await page.goto('rating-with-comment')
      const item = await page.getByTestId('rating-scale-item').locator('visible=true').first()
      const nextButton = await page.getByText('Далее')

      await item.click()
      await nextButton.click()

      // Перешли на следующий вопрос
      await expect(nextButton).toBeHidden()
    })

    test('Обязателен если опция \'Обязателен\' включена', async ({ page }) => {
      await page.goto('rating-with-comment-required')
      const firstRating = await page.getByTestId('rating-scale').first()
      const item = await page.getByTestId('rating-scale-item').locator('visible=true').first()
      const nextButton = await page.getByText('Далее')
      const error = await page.getByText('Обязательное поле')
      const textarea = await page.getByTestId('comment').locator('textarea')

      await expect(error).toBeHidden()
      await nextButton.click()
      await expect(error).toBeVisible()
      await expect(page).toHaveScreenshot('rating-with-comment-required-error.png')

      await item.click()
      await textarea.fill('some value')

      await expect(error).toBeHidden()
      await nextButton.click()

      // Перешли на следующий вопрос
      await expect(nextButton).toBeHidden()
    })

    test('Комплексный пример (Обязателен + валидация длины текста)', async ({ page }) => {
      await page.goto('rating-with-comment-complex')
      const firstRating = await page.getByTestId('rating-scale').first()
      const item = await page.getByTestId('rating-scale-item').locator('visible=true').first()
      const nextButton = await page.getByText('Далее')
      const requiredError = await page.getByText('Обязательное поле')
      const minLengthError = await page.getByText('Должно быть введено хотя бы 10 символов')
      const textarea = await page.getByTestId('comment').locator('textarea')

      await expect(requiredError).toBeHidden()
      await nextButton.click()
      await expect(requiredError).toBeVisible()
      await item.click()
      await textarea.fill('text')
      await expect(minLengthError).toBeVisible()
      await expect(page).toHaveScreenshot('rating-with-comment-complex-error.png')

      await textarea.fill('text with more then 10 characters')

      await expect(minLengthError).toBeHidden()
      await expect(requiredError).toBeHidden()
      await expect(page).toHaveScreenshot('rating-with-comment-complex-no-error.png')

      await nextButton.click()

      // Перешли на следующий вопрос
      await expect(nextButton).toBeHidden()
    })
  })

  test.describe('Уточняющий вопрос (текстовый вариант)', () => {
    test('Отображает уточняющий вопрос при клике на звездочку', async ({ page }) => {
      await page.goto('rating-with-assessments-text')
      const item = await page.getByTestId('rating-scale-item').locator('visible=true').last()
      const assessmentsLabel = await page.getByTestId('assessments-label').locator('visible=true')
      const assessmentsField = await page.getByTestId('assessments-text').locator('visible=true')

      await expect(assessmentsLabel).toBeHidden()
      await expect(assessmentsField).toBeHidden()
      await item.click()
      await expect(assessmentsField).toBeVisible()
      await expect(page).toHaveScreenshot('rating-with-assessments-text-clicked.png')
    })

    test('Уточняющий вопрос обязателен по умолчанию', async ({ page }) => {
      await page.goto('rating-with-assessments-text')
      const firstRating = await page.getByTestId('rating-scale').first()
      const nextButton = await page.getByText('Далее')
      const item = await page.getByTestId('rating-scale-item').locator('visible=true').last()
      const assessmentsField = await page.getByTestId('assessments-text')
      const error = await page.getByText('Обязательное поле')

      await item.click()
      await nextButton.click()
      await page.waitForTimeout(100)
      await expect(firstRating).toBeVisible()
      await expect(error).toBeVisible()
      await expect(page).toHaveScreenshot('rating-with-assessments-text-error.png')
      await assessmentsField.fill('some valueeeeeee')
      await expect(error).toBeHidden()
      await nextButton.click()

      // Перешли на следующий вопрос
      await expect(nextButton).toBeHidden()
    })

    test('Если рейтинг не выбран, уточняющий вопрос скрывается', async ({ page }) => {
      await page.goto('rating-with-assessments-text')
      const item = await page.getByTestId('rating-scale-item').locator('visible=true').last()
      const assessmentsField = await page.getByTestId('assessments-text').locator('visible=true')

      await item.click()
      await expect(assessmentsField).toBeVisible()
      await item.click()
      await page.waitForTimeout(500)
      await expect(assessmentsField).toBeHidden()
    })

    test('Если extraQuestionRateFrom/To определен, отображаем уточняющий вопрос только если попали в диапазон', async ({ page }) => {
      await page.goto('rating-with-assessments-text-complex')
      const itemThree = await page.getByTestId('rating-scale-item').locator('visible=true').nth(2)
      const itemFive = await page.getByTestId('rating-scale-item').locator('visible=true').last()
      const assessmentField = await page.getByTestId('assessments-text').locator('visible=true')

      await itemFive.click()
      await expect(assessmentField).toBeHidden()

      await itemThree.click()
      await expect(assessmentField).toBeVisible()
    })
  })

  test.describe('Уточняющий вопрос (единичный выбор)', () => {
    test('Отображает четыре чекбокса (три варианта и один самовариант) для УВ (с одним выбором)', async ({ page }) => {
      await page.goto('rating-with-assessments-single')
      const nextButton = await page.getByText('Далее')
      const item = await page.getByTestId('rating-scale-item').last()
      const assessmentChecks = await page.getByTestId('variants-check')

      await expect(assessmentChecks).toHaveCount(0)
      await item.click()
      await expect(assessmentChecks).toHaveCount(4)
      await expect(page).toHaveScreenshot('rating-with-assessments-single.png')

      await nextButton.click()
      // Перешли на следующий вопрос
      await expect(nextButton).toBeHidden()
    })
  })

  test('Отображает УВ с одним вариантом и включенной опцией \'Обязательный\'', async ({ page }) => {
    await page.goto('rating-with-assessments-single-required')
    const nextButton = await page.getByText('Далее')
    const item = await page.getByTestId('rating-scale-item').last()
    const assessmentChecks = await page.getByTestId('variants-check')
    const error = await page.getByText('Нужно выбрать один из вариантов')

    await item.click()
    await page.waitForTimeout(300)
    await nextButton.click()
    await expect(error).toBeVisible()
    await expect(page).toHaveScreenshot('rating-with-assessments-single-required-error.png')
    await assessmentChecks.first().click()
    await expect(error).toBeHidden()
    await nextButton.click()

    // Перешли на следующий вопрос
    await expect(nextButton).toBeHidden()
  })

  test('Отображает УВ с обязательным своим вариантом', async ({ page }) => {
    await page.goto('rating-with-assessments-single-required')
    const nextButton = await page.getByText('Далее')
    const item = await page.getByTestId('rating-scale-item').last()
    const assessmentChecks = await page.getByTestId('variants-check')
    const lastCheck = await assessmentChecks.last()
    const textarea = await page.locator('textarea')
    const requiredError = await page.getByText('Обязательное поле')
    const minLengthError = await page.getByText('Должно быть введено хотя бы 10 символов')

    await item.click()

    await lastCheck.click()
    await expect(textarea).toBeVisible()
    await nextButton.click()

    await expect(requiredError).toBeVisible()
    await page.waitForTimeout(100)
    await page.evaluate(() => {
      window.scrollTo(0, 0)
    })
    await expect(page).toHaveScreenshot('rating-with-assessments-single-selfvariant-required-error.png')

    await textarea.fill('text')
    await expect(minLengthError).toBeVisible()
    await page.waitForTimeout(100)
    await expect(page).toHaveScreenshot('rating-with-assessments-single-selfvariant-required-min-length-error.png')

    await textarea.fill('text with more then 10 characters')
    await expect(minLengthError).toBeHidden()
    await nextButton.click()

    // Перешли на следующий вопрос
    await expect(nextButton).toBeHidden()
  })

  test.describe('Уточняющий вопрос (множественный выбор)', () => {
    test('Отображает четыре чекбокса (три варианта и один самовариант) для УВ (с множественным выбором)', async ({ page }) => {
      await page.goto('rating-with-assessments-multiple')
      const nextButton = await page.getByText('Далее')
      const item = await page.getByTestId('rating-scale-item').last()
      const assessmentChecks = await page.getByTestId('variants-check')

      await expect(assessmentChecks).toHaveCount(0)
      await item.click()
      await expect(assessmentChecks).toHaveCount(4)
      await expect(page).toHaveScreenshot('rating-with-assessments-multiple.png')

      await nextButton.click()

      // Перешли на следующий вопрос
      await expect(nextButton).toBeHidden()
    })

    test('Отображает УВ с множественным вариантом и включенной опцией \'Обязательный\'', async ({ page }) => {
      await page.goto('rating-with-assessments-multiple-required')
      const nextButton = await page.getByText('Далее')
      const item = await page.getByTestId('rating-scale-item').last()
      const assessmentChecks = await page.getByTestId('variants-check')
      const error = await page.getByText('Нужно выбрать один из вариантов')

      await item.click()
      await page.waitForTimeout(300)
      await nextButton.click()
      await page.waitForTimeout(300)
      await expect(error).toBeVisible()
      await expect(page).toHaveScreenshot('rating-with-assessments-multiple-required-error.png')
      await assessmentChecks.first().click()
      await page.waitForTimeout(500)
      await expect(error).not.toBeVisible()
      await nextButton.click()

      // Перешли на следующий вопрос
      await expect(nextButton).toBeHidden()
    })

    test('Отображает УВ (множественный выбор) с обязательным своим вариантом', async ({ page }) => {
      await page.goto('rating-with-assessments-multiple-required')
      const firstRating = await page.getByTestId('rating-scale').first()
      const nextButton = await page.getByText('Далее')
      const item = await page.getByTestId('rating-scale-item').last()
      const assessmentChecks = await page.getByTestId('variants-check')
      const thirdCheck = await assessmentChecks.nth(2)
      const lastCheck = await assessmentChecks.last()
      const textarea = await page.locator('textarea')
      const requiredError = await page.getByText('Обязательное поле')
      const minLengthError = await page.getByText('Должно быть введено хотя бы 10 символов')

      await item.click()

      await thirdCheck.click()
      await lastCheck.click()
      await expect(textarea).toBeVisible()
      await nextButton.click()

      // Проверяем, что ошибка отображается
      await expect(requiredError).toBeVisible()
      await page.waitForTimeout(100)

      // scroll to top to avoid screenshot issues
      await page.evaluate(() => window.scrollTo(0, 0))
      await expect(page).toHaveScreenshot(
        'rating-with-assessments-multiple-selfvariant-required-error.png',
      )

      // Проверяем, что ошибка с минимальным количеством символов отображается
      await textarea.fill('text')
      await expect(minLengthError).toBeVisible()
      await page.waitForTimeout(100)
      await expect(page).toHaveScreenshot(
        'rating-with-assessments-multiple-selfvariant-required-min-length-error.png',
      )

      // Проверяем, что ошибки нет и перешли на следующий шаг
      await textarea.fill('text with more then 10 characters')
      await expect(minLengthError).toBeHidden()
      await nextButton.click()

      // Перешли на следующий вопрос
      await expect(nextButton).toBeHidden()
    })
  })
})
