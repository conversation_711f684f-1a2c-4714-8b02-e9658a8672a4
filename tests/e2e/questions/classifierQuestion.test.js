import { expect, test } from '@playwright/test'

// @TODO: Написать тесты для классификатора с опцией "Выпадающий список"
test.describe('Вопрос "классификатор"', () => {
  test('Базовый классификатор с одиночным выбором', async ({ page }) => {
    await page.goto('classifier-basic')
    const nextButton = await page.getByText('Далее')
    const classifierQuestion = await page.locator('.classifier-question')
    const error = await page.getByText('Нужно выбрать один из вариантов')
    // Check initial state
    await expect(classifierQuestion).toBeVisible()
    await expect(page).toHaveScreenshot('classifier-basic-initial.png')

    // Try to proceed without selection
    await nextButton.click()
    await expect(error).toBeVisible()
    await expect(page).toHaveScreenshot('classifier-basic-error.png')

    // Select an item
    const item = await page.locator('.classifier-tree-node__content:has-text("Элемент 3") .fc-check')
    await item.click()

    // Check that the error is gone and the item is selected
    await expect(error).not.toBeVisible()
    await expect(page.locator('.fc-check--checked')).toBeVisible()
    await expect(page).toHaveScreenshot('classifier-basic-selected.png')

    // Proceed to the next question
    await nextButton.click()
    await expect(classifierQuestion).not.toBeVisible()
  })

  test('Раскрытие и сворачивание категорий', async ({ page }) => {
    await page.goto('classifier-basic')

    // Check initial state (all categories are closed)
    await expect(page.locator('.classifier-tree-node__toggle--open')).toHaveCount(0)

    // Open a category
    const category1Toggle = await page.locator('.classifier-tree-node__content:has-text("Категория 1") .classifier-tree-node__toggle').first()
    await category1Toggle.click()

    // Check that the category is open
    await expect(page.locator('.classifier-tree-node__content:has-text("Категория 1") .classifier-tree-node__toggle--open')).toBeVisible()
    await expect(page).toHaveScreenshot('classifier-basic-category-open.png')

    // Close the category
    await category1Toggle.click()

    // Check that the category is closed again
    await expect(page.locator('.classifier-tree-node__content:has-text("Категория 1") .classifier-tree-node__toggle--open')).not.toBeVisible()
    await expect(page).toHaveScreenshot('classifier-basic-category-closed.png')
  })

  test('Выбор элемента из разных категорий', async ({ page }) => {
    await page.goto('classifier-basic')

    // open category 1
    await page.locator('.classifier-tree-node__content:has-text("Категория 1") .classifier-tree-node__toggle').first().click()

    // open category 11
    await page.locator('.classifier-tree-node__content:has-text("Категория 11") .classifier-tree-node__toggle').first().click()

    // Select an item from the first category
    await page.locator('.classifier-tree-node__content:has-text("Элемент 1") .fc-check').first().click()
    await expect(page.locator('.classifier-tree-node__content:has-text("Элемент 1") .fc-check--checked')).toBeVisible()

    // open category 2
    await page.locator('.classifier-tree-node__content:has-text("Категория 2") .classifier-tree-node__toggle').first().click()

    // open category 21
    await page.locator('.classifier-tree-node__content:has-text("Категория 21") .classifier-tree-node__toggle').first().click()

    // Select an item from another category
    await page.locator('.classifier-tree-node__content:has-text("Элемент 2") .fc-check').first().click()

    // Check that the first item is deselected and the second is selected
    await expect(page.locator('.classifier-tree-node__content:has-text("Элемент 1") .fc-check--checked')).not.toBeVisible()
    await expect(page.locator('.classifier-tree-node__content:has-text("Элемент 2") .fc-check--checked')).toBeVisible()

    await expect(page).toHaveScreenshot('classifier-basic-different-category-selection.png')
  })

  test('Проверка подсказок', async ({ page }) => {
    await page.goto('classifier-basic')

    // Check that hint triggers are present
    await expect(page.locator('.classifier-tree-node__hint-trigger')).toHaveCount(3)

    // Click on a hint trigger
    const hintTrigger = await page.locator('.classifier-tree-node__content:has-text("Категория 1") .classifier-tree-node__hint-trigger')
    await hintTrigger.hover()

    await page.waitForTimeout(400)
    await expect(page).toHaveScreenshot('classifier-basic-hint-visible.png')
  })
  test('Проверка подсказок на мобильной версии', async ({ page }) => {
    await page.goto('classifier-basic')
    await page.setViewportSize({ width: 375, height: 667 })
    const closeHintButton = await page.getByText('Закрыть')

    // Check that hint triggers are present
    await expect(page.locator('.classifier-tree-node__hint-trigger')).toHaveCount(3)

    // Click on a hint trigger
    const hintTrigger = await page.locator('.classifier-tree-node__content:has-text("Категория 1") .classifier-tree-node__hint-trigger')
    await hintTrigger.click()

    await page.waitForTimeout(400)
    await expect(page).toHaveScreenshot('classifier-basic-hint-visible-mobile.png')

    await closeHintButton.click()
    await expect(closeHintButton).not.toBeVisible()
  })
  test.describe('Классификатор с множественным выбором', () => {
    test('Базовая функциональность множественного выбора', async ({ page }) => {
      await page.goto('classifier-multiple-choice')
      const nextButton = await page.getByText('Далее')
      const classifierQuestion = await page.locator('.classifier-question--multiple')
      const error = await page.getByText('Нужно выбрать один из вариантов')

      // Check initial state (all items closed)
      await expect(classifierQuestion).toBeVisible()
      await expect(page.locator('.classifier-tree-node__toggle--open')).toHaveCount(0)
      await expect(page).toHaveScreenshot('classifier-multiple-choice-initial.png')

      // Open all categories
      await page.locator('.classifier-tree-node__toggle').nth(0).click()
      await page.locator('.classifier-tree-node__toggle').nth(1).click()
      await page.locator('.classifier-tree-node__toggle').nth(2).click()
      await page.locator('.classifier-tree-node__toggle').nth(3).click()

      await expect(page.locator('.classifier-tree-node__toggle--open')).toHaveCount(4)

      // Try to proceed without selection
      await nextButton.click()
      await expect(error).toBeVisible()
      await expect(page).toHaveScreenshot('classifier-multiple-choice-error.png')

      // Select multiple items
      await page.locator('.classifier-tree-node__content:has-text("Элемент 1") .fc-check').click()
      await page.locator('.classifier-tree-node__content:has-text("Элемент 3") .fc-check').click()

      // Check that the error is gone and the items are selected
      await expect(error).not.toBeVisible()
      await expect(page.locator('.classifier-tree-node__content:has-text("Элемент 1") .fc-check--checked')).toBeVisible()
      await expect(page.locator('.classifier-tree-node__content:has-text("Элемент 3") .fc-check--checked')).toBeVisible()
      await expect(page).toHaveScreenshot('classifier-multiple-choice-selected.png')

      // Proceed to the next question
      await nextButton.click()
      await expect(classifierQuestion).not.toBeVisible()
    })

    test('Проверка лимита выбора вариантов', async ({ page }) => {
      await page.goto('classifier-multiple-choice')

      // Категория 1 имеет 2 элемента. выбираем их

      // Сначала открываем категорию 1
      await page.locator('.classifier-tree-node__content:has-text("Категория 1") .classifier-tree-node__toggle').click()

      // Открываем Категорию 1 > Категорию 11
      await page.locator('.classifier-tree-node__content:has-text("Категория 11") .classifier-tree-node__toggle').click()

      // Выбираем элемент 1, 222
      await page.locator('.classifier-tree-node__content:has-text("Элемент 1") .fc-check').click()
      await page.locator('.classifier-tree-node__content:has-text("222") .fc-check').click()

      // Выбираем элемент 3
      await page.locator('.classifier-tree-node__content:has-text("Элемент 3") .fc-check').click()

      // ожидаем, что Категория 2 недоступна для выбора
      await expect(page.locator('.classifier-tree-node--disabled')).toBeVisible()

      await expect(page).toHaveScreenshot('classifier-multiple-choice-max-limit.png')
    })
  })
  test.describe('Классификатор с отображением "Простой список"', () => {
    test('Одиночный выбор', async ({ page }) => {
      await page.goto('classifier-list-type')
      const nextButton = await page.getByText('Далее')
      const error = await page.getByText('Нужно выбрать один из вариантов')
      const checks = await page.locator('.fc-check')
      const classifierQuestion = await page.locator('.classifier-question--list')

      await expect(nextButton).toBeVisible()
      await expect(page).toHaveScreenshot('classifier-list-type-initial.png')

      await expect(checks).toHaveCount(4)

      await nextButton.click()
      await expect(error).toBeVisible()
      await expect(page).toHaveScreenshot('classifier-list-type-error.png')

      await checks.first().click()

      await expect(page).toHaveScreenshot('classifier-list-type-selected.png')
      await nextButton.click()
      await expect(classifierQuestion).not.toBeVisible()
    })

    test('Множественный выбор', async ({ page }) => {
      await page.goto('classifier-list-type-multiple')
      const nextButton = await page.getByText('Далее')
      const error = await page.getByText('Нужно выбрать один из вариантов')
      const checks = await page.locator('.fc-check')
      const classifierQuestion = await page.locator('.classifier-question--list')

      await expect(nextButton).toBeVisible()
      await expect(page).toHaveScreenshot('classifier-list-type-multiple-initial.png')

      await expect(checks).toHaveCount(4)

      await nextButton.click()
      await expect(error).toBeVisible()
      await expect(page).toHaveScreenshot('classifier-list-type-multiple-error.png')

      await checks.first().click()
      await checks.nth(2).click()

      await expect(page).toHaveScreenshot('classifier-list-type-multiple-selected.png')
      await nextButton.click()
      await expect(classifierQuestion).not.toBeVisible()
    })
  })
})
