import { expect, test } from '@playwright/test'
import { getTestModes } from '../../testUtils'

for (const { name, urlParam, getScreenshotPath, viewport } of getTestModes(['default', 'simple'])) {
  test.describe(`Варинты ответов: Выпадающий список с файлами (${name})`, () => {
    test.beforeEach(async ({ page }) => {
      if (viewport) {
        await page.setViewportSize(viewport)
      }
    })

    test('variants-dropdown-files: renders dropdown with file previews', async ({ page }) => {
      await page.goto(`variants-dropdown-files${urlParam}`)
      const selectTrigger = await page.locator('.question-variants__file-select-trigger')

      await expect(selectTrigger).toBeVisible()
      await expect(page).toHaveScreenshot(getScreenshotPath('variants-dropdown-files-initial'))

      await selectTrigger.click()
      const firstOption = await page.locator('.command-item').first()
      await firstOption.click()
      await expect(page).toHaveScreenshot(getScreenshotPath('variants-dropdown-files-selected'))
    })

    test('variants-dropdown-files-multiple: renders dropdown with multiple file previews', async ({ page }) => {
      await page.goto(`variants-dropdown-files-multiple${urlParam}`)
      const selectTrigger = await page.locator('.question-variants__file-select-trigger')
      const questionTitle = await page.getByText('Варианты ответов').locator('visible=true')

      await expect(selectTrigger).toBeVisible()
      await expect(page).toHaveScreenshot(getScreenshotPath('variants-dropdown-files-multiple-initial'))

      await selectTrigger.click()
      const firstOption = await page.locator('.command-item').first()
      const secondOption = await page.locator('.command-item').nth(1)
      const selfVariant = await page.locator('.command-item').last()
      await firstOption.click()
      await secondOption.click()
      await selfVariant.click()

      await expect(page).toHaveScreenshot(getScreenshotPath('variants-dropdown-files-multiple-open'))

      await questionTitle.click()
      await questionTitle.click()
      await page.waitForTimeout(300)

      await expect(page).toHaveScreenshot(getScreenshotPath('variants-dropdown-files-multiple-selected'))
    })

    test('variants-dropdown-files-max-choose: only 2 items can be enabled, others are disabled', async ({ page }) => {
      await page.goto(`variants-dropdown-files-max-choose${urlParam}`)
      const selectTrigger = await page.locator('.question-variants__file-select-trigger')

      await expect(selectTrigger).toBeVisible()
      await expect(page).toHaveScreenshot(getScreenshotPath('variants-dropdown-files-max-choose-initial'))

      await selectTrigger.click()
      const firstOption = await page.locator('.command-item').first()
      const secondOption = await page.locator('.command-item').nth(1)
      await firstOption.click()
      await secondOption.click()
      await expect(page).toHaveScreenshot(getScreenshotPath('variants-dropdown-files-max-choose-two-selected'))
    })

    test('variants-dropdown-files-min-choose: shows different error "Необходимо выбрать хотя бы 2 варианта"', async ({ page }) => {
      await page.goto(`variants-dropdown-files-min-choose${urlParam}`)
      const selectTrigger = await page.locator('.question-variants__file-select-trigger')
      const nextButton = await page.getByTestId('poll-action-next')
      const questionTitle = await page.getByText('Варианты ответов').locator('visible=true')
      const error = await page.getByText('Необходимо выбрать хотя бы 2 варианта')

      await expect(selectTrigger).toBeVisible()
      await expect(page).toHaveScreenshot(getScreenshotPath('variants-dropdown-files-min-choose-initial'))

      await nextButton.click()
      await expect(error).toBeVisible()

      await questionTitle.click()
      await page.waitForTimeout(300)

      await selectTrigger.click()
      const firstOption = await page.locator('.command-item').first()
      await firstOption.click()

      await questionTitle.click()
      await page.waitForTimeout(300)

      await expect(error).toBeVisible()
      await expect(page).toHaveScreenshot(getScreenshotPath('variants-dropdown-files-min-choose-one-selected'))

      await selectTrigger.click()
      const secondOption = await page.locator('.command-item').nth(1)
      await secondOption.click()

      await questionTitle.click()
      await page.waitForTimeout(300)

      await expect(error).not.toBeVisible()
      await expect(page).toHaveScreenshot(getScreenshotPath('variants-dropdown-files-min-choose-two-selected'))
    })
  })
}
