/**
 * @file Содержит тесты для логических переходов со следующими типами вопросов:
 * - Выбор филиала
 * - Классификатор / Простой список
 * - Классификатор / Древовидный список
 * - Варианты ответов
 */
import { expect, test } from '@playwright/test'

// Helper function to check if pagination numbers are inactive
async function expectPaginationNumbersToBeInactive(page, numbers) {
  for (const number of numbers) {
    const paginatorItem = page.locator(`.fc-paginator-item[data-blocked="false"][data-active="false"][data-visible="false"] .fc-paginator-item__index:text-is("${number}")`)
    await expect(paginatorItem).toBeVisible()
  }
}

test.describe('Логические переходы: Часть 4', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('logic-part-4')
  })

  test.describe('Выбор филиала', () => {
    test('Переходит на стандартный конечный экран при выборе Москвы или Самары', async ({ page }) => {
      // Select Moscow branch
      await page.getByText('Москва').click()
      await page.getByText('Завершить').click()

      // Verify standard end screen
      await expect(page.getByText('Опрос успешно пройден!')).toBeVisible()
    })

    test('Переходит на экран пропуска при пропуске вопроса', async ({ page }) => {
      // Skip question without selecting anything
      await page.getByText('Завершить').click()

      // Verify skip screen
      await expect(page.getByText('Экран появится при пропуске вопроса')).toBeVisible()
    })
  })

  test.describe('Классификатор / Простой список', () => {
    test('Переходит на стандартный конечный экран при выборе "Элемент 1"', async ({ page }) => {
      await page.getByText('Санкт-Петербург').click()
      await page.getByText('Далее').click()

      // Select "Элемент 1"
      await page.getByText('Элемент 1').first().click()
      await page.getByText('Завершить').click()

      // Verify standard end screen
      await expect(page.getByText('Опрос успешно пройден!')).toBeVisible()
    })

    test('Переходит на экран пропуска при пропуске вопроса', async ({ page }) => {
      await page.getByText('Санкт-Петербург').click()
      await page.getByText('Далее').click()

      // Skip question
      await page.getByText('Завершить').click()

      // Verify skip screen
      await expect(page.getByText('Экран появится при пропуске вопроса')).toBeVisible()
    })
  })

  test.describe('Классификатор / Древовидный список', () => {
    test('Переходит на стандартный конечный экран при выборе "Элемент 1" или "Категория 2"', async ({ page }) => {
      await page.getByText('Санкт-Петербург').click()
      await page.getByText('Далее').click()
      await page.getByText('Элемент 2').first().click()
      await page.getByText('Далее').click()

      // Select "Элемент 1"
      await page.locator('.classifier-tree-node:has-text("Категория 1") button').last().click()
      await page.locator('.classifier-tree-node:has-text("Категория 11") button').last().click()
      await page.locator('.classifier-tree-node:has-text("Элемент 1") .fc-check').last().click()
      await page.getByText('Завершить').click()

      // Verify standard end screen
      await expect(page.getByText('Опрос успешно пройден!')).toBeVisible()
    })

    test('Переходит на экран пропуска при пропуске вопроса', async ({ page }) => {
      await page.getByText('Санкт-Петербург').click()
      await page.getByText('Далее').click()
      await page.getByText('Элемент 2').first().click()
      await page.getByText('Далее').click()

      // Skip question
      await page.getByText('Завершить').click()

      // Verify skip screen
      await expect(page.getByText('Экран появится при пропуске вопроса')).toBeVisible()
    })
  })

  test.describe('Варианты ответов', () => {
    test('Переходит на стандартный конечный экран при выборе "Свой вариант"', async ({ page }) => {
      await page.getByText('Санкт-Петербург').click()
      await page.getByText('Далее').click()
      await page.getByText('Элемент 2').first().click()
      await page.getByText('Далее').click()
      await page.getByText('Элемент 3', { exact: true }).first().click()
      await page.getByText('Далее').click()

      // Select and fill "Свой вариант"
      await page.getByText('Свой вариант').click()
      const input = page.locator('textarea')
      await input.fill('Custom answer')
      await page.getByText('Завершить').click()

      // Verify standard end screen
      await expect(page.getByText('Опрос успешно пройден!')).toBeVisible()
    })

    test('Переходит на экран пропуска при пропуске вопроса', async ({ page }) => {
      await page.getByText('Санкт-Петербург').click()
      await page.getByText('Далее').click()
      await page.getByText('Элемент 2').first().click()
      await page.getByText('Далее').click()
      await page.getByText('Элемент 3').first().click()
      await page.getByText('Далее').click()

      // Skip question
      await page.getByText('Завершить').click()

      // Verify skip screen
      await expect(page.getByText('Экран появится при пропуске вопроса')).toBeVisible()
    })
  })

  test('Корректно возвращается к предыдущим вопросам', async ({ page }) => {
    await page.goto('logic-part-4')
    // Navigate through all questions
    await page.getByText('Санкт-Петербург').click()
    await page.getByText('Далее').click()

    await page.getByText('Элемент 2').first().click()
    await page.getByText('Далее').click()

    await page.getByText('Элемент 3', { exact: true }).first().click()
    await page.getByText('Далее').click()

    await page.getByText('Свой вариант', { exact: true }).first().click()
    const input = page.locator('textarea')
    await input.fill('Custom answer')

    // Now go back through each question
    await page.getByText('Вернуться').click()
    await expect(page.getByText('Классификатор / Древовидный список')).toBeVisible()
    await page.waitForTimeout(100)

    await page.getByText('Вернуться').click()
    await expect(page.getByText('Классификатор / Простой список')).toBeVisible()
    await page.waitForTimeout(100)

    await page.getByText('Вернуться').click()
    await expect(page.getByText('Выбор филиала')).toBeVisible()

    // Verify no pagination numbers are inactive at the start
    await expectPaginationNumbersToBeInactive(page, [])
  })
})
