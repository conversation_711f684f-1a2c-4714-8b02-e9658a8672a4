import { expect, test } from '@playwright/test'

test.describe('Тип вопроса \'Анкета\'', () => {
  test('Отображает анкету и проверяет заполнение', async ({ page }) => {
    await page.goto('quiz-default')
    const nextButton = page.getByText('Далее')

    // 1. Make screenshots at the scroll top and bottom

    await expect(nextButton).toBeVisible()
    await expect(page).toHaveScreenshot('quiz-default-top.png')
    await page.evaluate(() => window.scrollTo(0, document.body.scrollHeight))
    await expect(page).toHaveScreenshot('quiz-default-bottom.png')

    // 2. Click "Далее" and check for errors
    await nextButton.click()

    await expect(page.getByText('Обязательное поле')).toHaveCount(12)

    // scroll to top to avoid screenshot issues
    await page.evaluate(() => window.scrollTo(0, 0))
    await expect(page).toHaveScreenshot('quiz-errors-top.png')
    await page.evaluate(() => window.scrollTo(0, document.body.scrollHeight))
    await expect(page).toHaveScreenshot('quiz-errors-bottom.png')

    // 3. Fill quiz with values
    await page.getByTestId('quiz-question-nomask-field').first().fill('Однострочный текст')

    // Много строчное поле не заполняем, так как оно не обязательное
    // await page.getByTestId('quiz-question-nomask-field').nth(1).fill('Многострочный текст')

    await page.getByTestId('quiz-question-phone-field').fill('9991234567')
    await page.getByTestId('quiz-question-email-field').fill('<EMAIL>')
    await page.getByTestId('quiz-question-number-field').fill('42')
    await page.getByTestId('quiz-question-site-field').fill('https://example.com')
    await page.getByTestId('quiz-question-surname-field').fill('Иванов')
    await page.getByTestId('quiz-question-name-field').fill('Иван')
    await page.getByTestId('quiz-question-patronymic-field').fill('Иванович')
    await page.getByTestId('quiz-question-date-field').fill('01.01.2024')
    await page.getByTestId('quiz-question-date-from-field').fill('01.01.2024')
    await page.getByTestId('quiz-question-date-to-field').fill('31.12.2024')
    await page.getByTestId('quiz-question-date-day-field').fill('15')
    await page.locator('.date-month-mask-container .select-trigger').click()
    await page.getByText('Март').click()

    await page.waitForTimeout(500)
    // scroll to top to avoid screenshot issues
    await page.evaluate(() => window.scrollTo(0, 0))
    await page.evaluate(() => window.scrollTo(0, 0))
    await page.waitForTimeout(300)

    await expect(page).toHaveScreenshot('quiz-filled-top.png')
    await page.evaluate(() => window.scrollTo(0, document.body.scrollHeight))
    await page.waitForTimeout(500)
    await expect(page).toHaveScreenshot('quiz-filled-bottom.png')

    // 4. Проверяем, что перешли на следующий вопрос и
    // анкета больше не видна
    await nextButton.click()
    await expect(page.locator('.quiz-question-fields')).not.toBeVisible()
  })
})
