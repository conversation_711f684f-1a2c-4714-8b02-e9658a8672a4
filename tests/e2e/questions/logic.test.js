import { expect, test } from '@playwright/test'
import { waitForUIStability } from '../testUtils'

// Helper function to check if pagination numbers are inactive
async function expectPaginationNumbersToBeInactive(page, numbers) {
  for (const number of numbers) {
    const paginatorItem = page.locator(`.fc-paginator-item[data-blocked="false"][data-active="false"][data-visible="false"] .fc-paginator-item__index:text-is("${number}")`)
    await expect(paginatorItem).toBeVisible()
  }
}

test.describe('Логические переходы', () => {
  test.describe('Логические переходы: Звездный рейтинг', () => {
    test('Переходит на вопрос "Рейтинг" экран при выборе трех звезд и возвращается обратно', async ({ page }) => {
      await page.goto('logic-part-1')
      // Skip start screen
      await page.getByText('Пройти опрос').click()

      // Click third star and submit
      const stars = page.getByTestId('star-rating-item')
      await stars.nth(2).click() // 3rd star (index 2)
      await waitForUIStability(page)
      await page.getByText('Далее').click()

      // Verify we're on Rating screen
      await expect(page.getByText('Рейтинг')).toBeVisible()

      // Verify pagination numbers are inactive
      await expectPaginationNumbersToBeInactive(page, [2, 3, 4, 5])

      // Click back button and verify we're back on star rating
      await page.getByText('Вернуться').click()
      await waitForUIStability(page)
      await expect(page.getByTestId('star-rating-item').first()).toBeVisible()

      // Verify no pagination numbers are inactive
      await expectPaginationNumbersToBeInactive(page, [])
    })
    test('Переходит на конечный экран при выборе двух звезд', async ({ page }) => {
      await page.goto('logic-part-1')
      // Skip start screen
      await page.getByText('Пройти опрос').click()

      // Click second star and submit
      const stars = page.getByTestId('star-rating-item')
      await stars.nth(1).click() // 2nd star (index 1)
      await waitForUIStability(page)
      await page.getByText('Завершить').click()

      // Verify end screen is shown
      await expect(page.getByText('конец 1')).toBeVisible()
    })

    test('Переходит на стандартный конечный экран при выборе пяти звезд', async ({ page }) => {
      await page.goto('logic-part-1')
      // Skip start screen
      await page.getByText('Пройти опрос').click()

      // Click fifth star and submit
      const stars = page.getByTestId('star-rating-item')
      await stars.nth(4).click() // 5th star (index 4)
      await waitForUIStability(page)
      await page.getByText('Завершить').click()

      // Verify standard end screen is shown
      await expect(page.getByText('Опрос успешно пройден!')).toBeVisible()
    })

    test('Переходит на вопрос "Рейтинг" экран при выборе трех звезд', async ({ page }) => {
      await page.goto('logic-part-1')
      // Skip start screen
      await page.getByText('Пройти опрос').click()

      // Click third star and submit
      const stars = page.getByTestId('star-rating-item')
      await stars.nth(2).click() // 3rd star (index 2)
      await waitForUIStability(page)
      await page.getByText('Далее').click()

      // Verify we're on Rating screen
      await expect(page.getByText('Рейтинг')).toBeVisible()

      // Verify pagination numbers are inactive
      await expectPaginationNumbersToBeInactive(page, [2, 3, 4, 5])
    })
  })

  test.describe('Логические переходы: Рейтинг', () => {
    test('Переходит на конечный экран при выборе 6 в "Рейтинг - шкала 7"', async ({ page }) => {
      await page.goto('logic-part-1')
      // Skip start screen
      await page.getByText('Пройти опрос').click()

      // Click third star and submit to get to Rating
      const stars = page.getByTestId('star-rating-item')
      await stars.nth(2).click() // 3rd star (index 2)
      await waitForUIStability(page)
      await page.getByText('Далее').click()

      // Verify we're on Rating screen
      await expect(page.getByText('Рейтинг')).toBeVisible()

      // Verify pagination numbers are inactive
      await expectPaginationNumbersToBeInactive(page, [2, 3, 4, 5])

      // Click "3" and submit
      const ratingItems = page.getByTestId('rating-scale-item')
      await ratingItems.nth(2).click() // 3rd rating (index 2)
      await waitForUIStability(page)
      await page.getByText('Далее').click()

      // Verify we're on "Рейтинг - шкала 7" screen
      await expect(page.getByText('Рейтинг - шкала 7')).toBeVisible()

      // Click "7" and verify end screen
      const scale7Items = page.getByTestId('rating-scale-item')
      await scale7Items.nth(6).click() // 7th rating (index 6)
      await waitForUIStability(page, 500)

      const finishButton = page.getByText('Завершить')
      await expect(finishButton).toBeVisible({ timeout: 10000 })
      await finishButton.click()

      // Add a wait for the UI to update
      await expect(page.getByText('конец 1')).toBeVisible()
    })

    test('Переходит на "конец 2" при пропуске вопроса "Рейтинг"', async ({ page }) => {
      await page.goto('logic-part-1')
      // Skip start screen
      await page.getByText('Пройти опрос').click()

      // Click third star and submit to get to Rating
      const stars = page.getByTestId('star-rating-item')
      await stars.nth(2).click() // 3rd star (index 2)
      await waitForUIStability(page)
      await page.getByText('Далее').click()

      // Verify we're on Rating screen
      await expect(page.getByText('Рейтинг')).toBeVisible()

      // Verify pagination numbers are inactive
      await expectPaginationNumbersToBeInactive(page, [2, 3, 4, 5])

      // Click "Далее" without selecting any rating
      await page.getByText('Завершить').click()

      // Verify end screen "конец 2" is shown
      await expect(page.getByText('конец 2')).toBeVisible()
    })

    test('Переходит на "Смайл-рейтинг" при выборе 2 в "Рейтинг - шкала 7"', async ({ page }) => {
      await page.goto('logic-part-1')
      // Skip start screen
      await page.getByText('Пройти опрос').click()

      // Click third star and submit to get to Rating
      const stars = page.getByTestId('star-rating-item')
      await stars.nth(2).click() // 3rd star (index 2)
      await waitForUIStability(page)
      await page.getByText('Далее').click()

      // Verify we're on Rating screen
      await expect(page.getByText('Рейтинг')).toBeVisible()

      // Verify pagination numbers are inactive
      await expectPaginationNumbersToBeInactive(page, [2, 3, 4, 5])

      // Click "3" and submit
      const ratingItems = page.getByTestId('rating-scale-item')
      await ratingItems.nth(2).click() // 3rd rating (index 2)
      await waitForUIStability(page)
      await page.getByText('Далее').click()

      // Verify we're on "Рейтинг - шкала 7" screen
      await expect(page.getByText('Рейтинг - шкала 7')).toBeVisible()

      // Click "2" and verify smile rating screen
      const scale7Items = page.getByTestId('rating-scale-item')
      await scale7Items.nth(1).click() // 2nd rating (index 1)
      await waitForUIStability(page)
      await page.getByText('Далее').click()

      // Verify smile rating screen is shown
      await expect(page.getByText('Смайл-рейтинг')).toBeVisible()

      // Verify pagination numbers 2, 3, 4, 5, 8 are inactive
      await expectPaginationNumbersToBeInactive(page, [2, 3, 4, 5, 8])
    })
  })

  test.describe('Логические переходы: Смайл-рейтинг', () => {
    test('Переходит на "Смайл-рейтинг / лицо" при выборе второго смайла', async ({ page }) => {
      await page.goto('logic-part-1')

      // Skip start screen
      await page.getByText('Пройти опрос').click()

      // Navigate through star rating -> rating -> rating scale 7 -> smile rating
      const stars = page.getByTestId('star-rating-item')
      await stars.nth(2).click()
      await waitForUIStability(page)
      await page.getByText('Далее').click()

      const ratingItems = page.getByTestId('rating-scale-item')
      await ratingItems.nth(2).click()
      await waitForUIStability(page)
      await page.getByText('Далее').click()

      const scale7Items = page.getByTestId('rating-scale-item')
      await scale7Items.nth(1).click()
      await waitForUIStability(page)
      await page.getByText('Далее').click()

      // Verify we're on Smile Rating screen
      await expect(page.getByText('Смайл-рейтинг')).toBeVisible()

      // Select second smile item
      const smileItems = page.getByTestId('smile-rating-item')
      await smileItems.nth(1).click()

      // Verify pagination numbers are inactive
      await expectPaginationNumbersToBeInactive(page, [2, 3, 4, 5, 8, 10])

      await page.getByText('Далее').click()
      await expect(page.getByText('Смайл-рейтинг / лицо')).toBeVisible()
    })
  })

  test.describe('Логические переходы: Смайл-рейтинг / лицо', () => {
    test('Переходит на стандартный конечный экран если ничего не выбрано', async ({ page }) => {
      // Navigate to "Смайл-рейтинг / лицо" through the required path
      await page.goto('logic-part-1')
      await page.getByText('Пройти опрос').click()

      // Navigate through star rating -> rating -> rating scale 7 -> smile rating -> smile rating/face
      const stars = page.getByTestId('star-rating-item')
      await stars.nth(2).click() // 3rd star
      await waitForUIStability(page)
      await page.getByText('Далее').click()

      const ratingItems = page.getByTestId('rating-scale-item')
      await ratingItems.nth(2).click() // 3rd rating
      await waitForUIStability(page)
      await page.getByText('Далее').click()

      const scale7Items = page.getByTestId('rating-scale-item')
      await scale7Items.nth(1).click() // 2nd rating
      await waitForUIStability(page)
      await page.getByText('Далее').click()

      const smileItems = page.getByTestId('smile-rating-item')
      await smileItems.nth(1).click() // 2nd smile
      await waitForUIStability(page)
      await page.getByText('Далее').click()

      // On "Смайл-рейтинг / лицо" screen, click "Завершить" without selecting anything
      await page.getByText('Завершить').click()

      // Verify standard end screen is shown
      await expect(page.getByText('Опрос успешно пройден!')).toBeVisible()
    })

    test('Переходит на "конец 2" при выборе третьего смайла', async ({ page }) => {
      // Navigate to "Смайл-рейтинг / лицо" through the required path
      await page.goto('logic-part-1')
      await page.getByText('Пройти опрос').click()

      // Navigate through star rating -> rating -> rating scale 7 -> smile rating -> smile rating/face
      const stars = page.getByTestId('star-rating-item')
      await stars.nth(2).click() // 3rd star
      await waitForUIStability(page)
      await page.getByText('Далее').click()

      const ratingItems = page.getByTestId('rating-scale-item')
      await ratingItems.nth(2).click() // 3rd rating
      await waitForUIStability(page)
      await page.getByText('Далее').click()

      const scale7Items = page.getByTestId('rating-scale-item')
      await scale7Items.nth(1).click() // 2nd rating
      await waitForUIStability(page)
      await page.getByText('Далее').click()

      const smileItems = page.getByTestId('smile-rating-item')
      await smileItems.nth(1).click() // 2nd smile
      await waitForUIStability(page)
      await page.getByText('Далее').click()

      // On "Смайл-рейтинг / лицо" screen, select 3rd smile and submit
      const faceSmileItems = page.getByTestId('smile-rating-item')
      await faceSmileItems.nth(2).click() // 3rd smile
      await waitForUIStability(page)
      await page.getByText('Завершить').click()

      // Verify "конец 2" screen is shown
      await expect(page.getByText('конец 2')).toBeVisible()
    })

    test('Переходит на "Рейтинг NPS / Стандартный" при выборе пятого смайла', async ({ page }) => {
      // Navigate to "Смайл-рейтинг / лицо" through the required path
      await page.goto('logic-part-1')
      await page.getByText('Пройти опрос').click()

      // Navigate through star rating -> rating -> rating scale 7 -> smile rating -> smile rating/face
      const stars = page.getByTestId('star-rating-item')
      await stars.nth(2).click() // 3rd star
      await waitForUIStability(page)
      await page.getByText('Далее').click()

      const ratingItems = page.getByTestId('rating-scale-item')
      await ratingItems.nth(2).click() // 3rd rating
      await waitForUIStability(page)
      await page.getByText('Далее').click()

      const scale7Items = page.getByTestId('rating-scale-item')
      await scale7Items.nth(1).click() // 2nd rating
      await waitForUIStability(page)
      await page.getByText('Далее').click()

      const smileItems = page.getByTestId('smile-rating-item')
      await smileItems.nth(1).click() // 2nd smile
      await waitForUIStability(page)
      await page.getByText('Далее').click()

      // On "Смайл-рейтинг / лицо" screen, select 5th smile and submit
      const faceSmileItems = page.getByTestId('smile-rating-item')
      await faceSmileItems.last().click() // 5th smile
      await waitForUIStability(page)
      await page.getByText('Далее').click()

      // Verify "Рейтинг NPS / Стандартный" screen is shown
      await expect(page.getByText('Рейтинг NPS / Стандартный')).toBeVisible()

      // Verify pagination numbers are inactive
      await expectPaginationNumbersToBeInactive(page, [2, 3, 4, 5, 8, 10, 12, 13])
    })
  })
})

test.describe('Логические переходы: Рейтинг NPS / Стандартный', () => {
  test('Переходит на стандартный конечный экран при выборе оценки 0 или 10', async ({ page }) => {
    // Navigate to NPS Rating through the required path
    await page.goto('logic-part-1')
    await page.getByText('Пройти опрос').click()

    // Navigate through: star rating -> rating -> rating scale 7 -> smile rating -> smile rating/face -> NPS Rating
    const stars = page.getByTestId('star-rating-item')
    await stars.nth(2).click() // 3rd star
    await waitForUIStability(page)
    await page.getByText('Далее').click()

    const ratingItems = page.getByTestId('rating-scale-item')
    await ratingItems.nth(2).click() // 3rd rating
    await waitForUIStability(page)
    await page.getByText('Далее').click()

    const scale7Items = page.getByTestId('rating-scale-item')
    await scale7Items.nth(1).click() // 2nd rating
    await waitForUIStability(page)
    await page.getByText('Далее').click()

    const smileItems = page.getByTestId('smile-rating-item')
    await smileItems.nth(1).click() // 2nd smile
    await waitForUIStability(page)
    await page.getByText('Далее').click()

    const faceSmileItems = page.getByTestId('smile-rating-item')
    await faceSmileItems.last().click() // 5th smile
    await waitForUIStability(page)
    await page.getByText('Далее').click()

    // On NPS Rating screen, test both 0 and 10 ratings
    const npsItems = page.getByTestId('rating-nps-item')

    // Test with rating 0
    await npsItems.first().click() // 0 rating
    await waitForUIStability(page)
    await page.getByText('Завершить').click()
    await expect(page.getByText('Опрос успешно пройден!')).toBeVisible()

    // Navigate back to test rating 10
    await page.goto('logic-part-1')
    await page.getByText('Пройти опрос').click()

    // Repeat navigation
    await stars.nth(2).click()
    await waitForUIStability(page)
    await page.getByText('Далее').click()
    await ratingItems.nth(2).click()
    await waitForUIStability(page)
    await page.getByText('Далее').click()
    await scale7Items.nth(1).click()
    await waitForUIStability(page)
    await page.getByText('Далее').click()
    await smileItems.nth(1).click()
    await waitForUIStability(page)
    await page.getByText('Далее').click()
    await faceSmileItems.last().click()
    await waitForUIStability(page)
    await page.getByText('Далее').click()

    // Test with rating 10
    await npsItems.last().click() // 10 rating
    await waitForUIStability(page)
    await page.getByText('Завершить').click()
    await expect(page.getByText('Опрос успешно пройден!')).toBeVisible()
  })
})

test.describe('Логические переходы: Рейтинг NPS / Стандартный с 1', () => {
  test('Переходит на стандартный конечный экран при выборе оценки 1 или 10', async ({ page }) => {
    // Navigate to NPS Rating through the required path
    await page.goto('logic-part-1')
    await page.getByText('Пройти опрос').click()

    // Navigate through: star rating -> rating -> rating scale 7 -> smile rating -> smile rating/face -> NPS Rating -> NPS Rating с 1
    const stars = page.getByTestId('star-rating-item')
    await stars.nth(2).click() // 3rd star
    await waitForUIStability(page)
    await page.getByText('Далее').click()

    const ratingItems = page.getByTestId('rating-scale-item')
    await ratingItems.nth(2).click() // 3rd rating
    await waitForUIStability(page)
    await page.getByText('Далее').click()

    const scale7Items = page.getByTestId('rating-scale-item')
    await scale7Items.nth(1).click() // 2nd rating
    await waitForUIStability(page)
    await page.getByText('Далее').click()

    const smileItems = page.getByTestId('smile-rating-item')
    await smileItems.nth(1).click() // 2nd smile
    await waitForUIStability(page)
    await page.getByText('Далее').click()

    const faceSmileItems = page.getByTestId('smile-rating-item')
    await faceSmileItems.last().click() // 5th smile
    await waitForUIStability(page)
    await page.getByText('Далее').click()

    // Navigate through first NPS Rating
    const firstNpsItems = page.getByTestId('rating-nps-item')
    await firstNpsItems.nth(5).click() // Select rating 5 to continue
    await waitForUIStability(page)
    await page.getByText('Далее').click()

    // On second NPS Rating screen, test both 1 and 10 ratings
    const npsItems = page.getByTestId('rating-nps-item')

    // Test with rating 1
    await npsItems.nth(1).click() // 1 rating
    await waitForUIStability(page)
    await page.getByText('Завершить').click()
    await expect(page.getByText('Опрос успешно пройден!')).toBeVisible()

    // Navigate back to test rating 10
    await page.goto('logic-part-1')
    await page.getByText('Пройти опрос').click()

    // Repeat navigation
    await stars.nth(2).click()
    await waitForUIStability(page)
    await page.getByText('Далее').click()
    await ratingItems.nth(2).click()
    await waitForUIStability(page)
    await page.getByText('Далее').click()
    await scale7Items.nth(1).click()
    await waitForUIStability(page)
    await page.getByText('Далее').click()
    await smileItems.nth(1).click()
    await waitForUIStability(page)
    await page.getByText('Далее').click()
    await faceSmileItems.last().click()
    await waitForUIStability(page)
    await page.getByText('Далее').click()
    await firstNpsItems.nth(5).click()
    await waitForUIStability(page)
    await page.getByText('Далее').click()

    // Test with rating 10
    await npsItems.last().click() // 10 rating
    await waitForUIStability(page)
    await page.getByText('Завершить').click()
    await expect(page.getByText('Опрос успешно пройден!')).toBeVisible()
  })
})

test.describe('Логические переходы: Шкала / Стандартная', () => {
  test('Переходит на "конец 1" при выборе значения от 62 до 100', async ({ page }) => {
    // Navigate to Scale Rating through the required path
    await page.goto('logic-part-1')
    await page.getByText('Пройти опрос').click()

    // Navigate through: star rating -> rating -> rating scale 7 -> smile rating -> smile rating/face -> NPS Rating -> NPS Rating с 1 -> Scale
    const stars = page.getByTestId('star-rating-item')
    await stars.nth(2).click() // 3rd star
    await waitForUIStability(page)
    await page.getByText('Далее').click()

    const ratingItems = page.getByTestId('rating-scale-item')
    await ratingItems.nth(2).click() // 3rd rating
    await waitForUIStability(page)
    await page.getByText('Далее').click()

    const scale7Items = page.getByTestId('rating-scale-item')
    await scale7Items.nth(1).click() // 2nd rating
    await waitForUIStability(page)
    await page.getByText('Далее').click()

    const smileItems = page.getByTestId('smile-rating-item')
    await smileItems.nth(1).click() // 2nd smile
    await waitForUIStability(page)
    await page.getByText('Далее').click()

    const faceSmileItems = page.getByTestId('smile-rating-item')
    await faceSmileItems.last().click() // 5th smile
    await waitForUIStability(page)
    await page.getByText('Далее').click()

    const firstNpsItems = page.getByTestId('rating-nps-item')
    await firstNpsItems.nth(5).click() // Select rating 5 to continue
    await waitForUIStability(page)
    await page.getByText('Далее').click()

    const secondNpsItems = page.getByTestId('rating-nps-item')
    await secondNpsItems.nth(3).click() // Select rating 3 to continue
    await waitForUIStability(page)
    await page.getByText('Далее').click()

    // On Scale Rating screen, set value to 80 (between 62 and 100)
    const input = page.locator('.fc-input.scale-input input')
    await input.fill('80')
    await input.blur()

    await page.getByText('Завершить').click()
    await expect(page.getByText('конец 1')).toBeVisible()
  })

  test('Переходит на "конец 2" при выборе значения вне диапазона 62-100', async ({ page }) => {
    // Navigate to Scale Rating through the required path
    await page.goto('logic-part-1')
    await page.getByText('Пройти опрос').click()

    // Navigate through previous questions
    const stars = page.getByTestId('star-rating-item')
    await stars.nth(2).click()
    await waitForUIStability(page)
    await page.getByText('Далее').click()

    const ratingItems = page.getByTestId('rating-scale-item')
    await ratingItems.nth(2).click()
    await waitForUIStability(page)
    await page.getByText('Далее').click()

    const scale7Items = page.getByTestId('rating-scale-item')
    await scale7Items.nth(1).click()
    await waitForUIStability(page)
    await page.getByText('Далее').click()

    const smileItems = page.getByTestId('smile-rating-item')
    await smileItems.nth(1).click()
    await waitForUIStability(page)
    await page.getByText('Далее').click()

    const faceSmileItems = page.getByTestId('smile-rating-item')
    await faceSmileItems.last().click()
    await waitForUIStability(page)
    await page.getByText('Далее').click()

    const firstNpsItems = page.getByTestId('rating-nps-item')
    await firstNpsItems.nth(5).click()
    await waitForUIStability(page)
    await page.getByText('Далее').click()

    const secondNpsItems = page.getByTestId('rating-nps-item')
    await secondNpsItems.nth(3).click()
    await waitForUIStability(page)
    await page.getByText('Далее').click()

    // On Scale Rating screen, set value to 50 (outside 62-100 range)
    const slider = page.locator('.slider-thumb')
    await slider.dragTo(page.locator('.slider-track'), {
      targetPosition: { x: 50, y: 0 },
    })

    await page.getByText('Завершить').click()
    await expect(page.getByText('конец 2')).toBeVisible()
  })

  test('Корректно возвращается к первому вопросу через кнопку "Вернуться"', async ({ page }) => {
    // Navigate to Scale Rating through the required path
    await page.goto('logic-part-1')
    await page.getByText('Пройти опрос').click()

    // Navigate through all questions to reach Scale Rating
    const stars = page.getByTestId('star-rating-item')
    await stars.nth(2).click()
    await waitForUIStability(page)
    await page.getByText('Далее').click()

    const ratingItems = page.getByTestId('rating-scale-item')
    await ratingItems.nth(2).click()
    await waitForUIStability(page)
    await page.getByText('Далее').click()

    const scale7Items = page.getByTestId('rating-scale-item')
    await scale7Items.nth(1).click()
    await waitForUIStability(page)
    await page.getByText('Далее').click()

    const smileItems = page.getByTestId('smile-rating-item')
    await smileItems.nth(1).click()
    await waitForUIStability(page)
    await page.getByText('Далее').click()

    const faceSmileItems = page.getByTestId('smile-rating-item')
    await faceSmileItems.last().click()
    await waitForUIStability(page)
    await page.getByText('Далее').click()

    const firstNpsItems = page.getByTestId('rating-nps-item')
    await firstNpsItems.nth(5).click()
    await waitForUIStability(page)
    await page.getByText('Далее').click()

    const secondNpsItems = page.getByTestId('rating-nps-item')
    await secondNpsItems.nth(3).click()
    await waitForUIStability(page)
    await page.getByText('Далее').click()

    // Now we're on Scale Rating screen
    // Click "Вернуться" multiple times to get back to first question
    for (let i = 0; i < 7; i++) {
      await page.getByText('Вернуться').click()
      await page.waitForTimeout(100)
    }

    // Verify we're back at the first star rating question
    await expect(page.getByTestId('star-rating-item').first()).toBeVisible()

    // Verify no pagination numbers are inactive
    await expectPaginationNumbersToBeInactive(page, [])
  })
})
