import { expect, test } from '@playwright/test'

test.describe('Вопрос \'медиа варианты\'', () => {
  test('Отображает медиа варианты с одиночным выбором', async ({ page }) => {
    await page.goto('media-variants-default')
    const nextButton = await page.getByText('Далее')
    const galleryItems = await page.locator('.gallery-item')
    const selectButtons = await page.locator('.gallery-item__select-button')
    const nextSlideButton = await page.locator('.gallery__button-next')

    await expect(galleryItems).toHaveCount(3)
    await expect(page).toHaveScreenshot('media-variants-default.png')

    // Выбираем первый вариант
    await selectButtons.first().click()
    await expect(page).toHaveScreenshot('media-variants-default-selected.png')

    // Переходим к следующему слайду
    await nextSlideButton.click()
    await expect(page).toHaveScreenshot('media-variants-default-next-slide.png')

    // Открываем fancybox для последнего слайда
    await galleryItems.last().locator('.gallery-item__preview-trigger').click()
    const fancybox = await page.locator('.fancybox__container')
    await expect(fancybox).toBeVisible()

    await fancybox.press('Escape')
    await fancybox.press('Escape')
    // Переходим на следующий вопрос
    await nextButton.click()
    await expect(galleryItems.first()).not.toBeVisible()
  })

  test('Отображает медиа варианты с множественным выбором', async ({ page }) => {
    await page.goto('media-variants-multiple')
    const galleryItems = await page.locator('.gallery-item')
    const selectButtons = await page.locator('.gallery-item__select-button')

    await expect(galleryItems).toHaveCount(3)
    await expect(page).toHaveScreenshot('media-variants-multiple.png')

    // Выбираем первый и второй варианты
    await selectButtons.nth(0).click()
    await selectButtons.nth(1).click()
    await expect(page).toHaveScreenshot('media-variants-multiple-selected.png')

    // Проверяем, что оба варианта выбраны
    await expect(galleryItems.nth(0)).toHaveClass(/gallery-item--selected/)
    await expect(galleryItems.nth(1)).toHaveClass(/gallery-item--selected/)
  })

  test('Ограничение выбора двумя вариантами', async ({ page }) => {
    await page.goto('media-variants-max-choose-2')
    const galleryItems = await page.locator('.gallery-item')
    const selectButtons = await page.locator('.gallery-item__select-button')

    await expect(galleryItems).toHaveCount(3)

    // Выбираем первые два варианта
    await selectButtons.nth(0).click()
    await selectButtons.nth(1).click()

    // Проверяем, что третий вариант недоступен для выбора
    await expect(galleryItems.last()).toHaveClass(/gallery-item--disabled/)
  })

  test('Функциональность пропуска', async ({ page }) => {
    await page.goto('media-variants-skip')
    const galleryItems = await page.locator('.gallery-item')
    const selectButtons = await page.locator('.gallery-item__select-button')
    const skipCheckbox = await page.getByText('Затрудняюсь ответить')

    await expect(galleryItems).toHaveCount(3)

    // Выбираем первый вариант
    await selectButtons.first().click()
    await galleryItems.first().scrollIntoViewIfNeeded()

    await page.evaluate(() => {
      window.scrollTo(0, 0)
    })

    await expect(page).toHaveScreenshot('media-variants-skip-selected.png')

    // Активируем пропуск
    await skipCheckbox.click()

    await page.evaluate(() => {
      window.scrollTo(0, 0)
    })
    await expect(page).toHaveScreenshot('media-variants-skip-activated.png')

    // Проверяем, что выбор сброшен
    await expect(galleryItems.first()).not.toHaveClass(/gallery-item--selected/)
  })
})
