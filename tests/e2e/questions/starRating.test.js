import { expect, test } from '@playwright/test'
import { getTestModes } from '../testUtils'

for (const { name, urlParam, getScreenshotPath, viewport, setupPage } of getTestModes()) {
  test.describe(`Вопрос 'звездный рейтинг' (${name})`, () => {
    test.beforeEach(async ({ page }) => {
      if (viewport) {
        await page.setViewportSize(viewport)
      }
      if (setupPage) {
        await setupPage(page)
      }
    })

    test('Отображает 5 звездочек по умолчанию', async ({ page }) => {
      await page.goto(`star-rating${urlParam}`)
      const stars = await page.getByTestId('star-rating-item')
      await expect(stars).toHaveCount(5)
      await expect(page).toHaveScreenshot(getScreenshotPath('star-rating'))
    })

    test('Отображает 2 звездочки', async ({ page }) => {
      await page.goto(`star-rating-2-stars${urlParam}`)
      const stars = await page.getByTestId('star-rating-item')
      await expect(stars).toHaveCount(2)
      await expect(page).toHaveScreenshot(getScreenshotPath('star-rating-2-stars'))
    })

    test('Отображает 2 звездочки (большие)', async ({ page }) => {
      await page.goto(`star-rating-2-stars-big${urlParam}`)
      const stars = await page.getByTestId('star-rating-item')
      await expect(stars).toHaveCount(2)
      await expect(page).toHaveScreenshot(getScreenshotPath('star-rating-2-stars-big'))
    })

    test('Отображает 10 звездочек', async ({ page }) => {
      await page.goto(`star-rating-10-stars${urlParam}`)
      const stars = await page.getByTestId('star-rating-item')
      await expect(stars).toHaveCount(10)
      await expect(page).toHaveScreenshot(getScreenshotPath('star-rating-10-stars'))
    })

    test('Отображает 10 звездочек с номерами', async ({ page }) => {
      await page.goto(`star-rating-10-stars-with-numbers${urlParam}`)
      const stars = await page.getByTestId('star-rating-item')
      await expect(stars).toHaveCount(10)
      const numbers = await page.getByTestId('star-number')
      await expect(numbers).toHaveCount(10)
      await expect(numbers).toHaveText([
        '1',
        '2',
        '3',
        '4',
        '5',
        '6',
        '7',
        '8',
        '9',
        '10',
      ])
      await expect(page).toHaveScreenshot(
        getScreenshotPath('star-rating-10-stars-with-numbers'),
      )
    })

    test('Отображает метку по клике на звезду', async ({ page }) => {
      await page.goto(`star-rating-with-labels${urlParam}`)
      const star = await page.getByTestId('star-rating-item').first()
      const label = await page.getByTestId('active-label')
      await expect(label).not.toBeVisible()
      await star.click()
      if (name === 'Планшетный вид') {
        await expect(star).not.toBeVisible()
      } else {
        await expect(label).toBeVisible()
        await expect(page).toHaveScreenshot(getScreenshotPath('star-rating-with-labels'))
      }
    })

    test('Всегда отображает метки при включенной опции \'Всегда отображать метки\'', async ({
      page,
    }) => {
      await page.goto(`star-rating-always-show-labels${urlParam}`)
      const labels = await page.getByTestId('star-label')
      await expect(labels).toHaveCount(5)
      await expect(labels).toHaveText([
        'метка 1',
        'метка 2',
        'метка 3',
        'метка 4',
        'метка 5',
      ])
      await expect(page).toHaveScreenshot(getScreenshotPath('star-rating-always-show-labels'))
    })
  })
}
