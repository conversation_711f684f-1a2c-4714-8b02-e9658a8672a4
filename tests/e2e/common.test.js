import { expect, test } from '@playwright/test'

test.describe('Опросы', () => {
  test('Отображается страница опроса', async ({ page }) => {
    await page.goto('default')
    await expect(page.getByText('Звездный рейтинг')).toBeVisible()
  })
  test('Отображается следующий вопрос при клике на кнопку "Далее"', async ({
    page,
  }) => {
    await page.goto('default-2-questions')
    const starRating = page.getByTestId('star-rating')
    const starItems = page.getByTestId('star-rating-item')
    const nextButton = page.getByText('Далее')
    await expect(starItems).toHaveCount(5)

    await starItems.nth(0).click()
    await expect(nextButton).toBeEnabled()
    await nextButton.click()
    await expect(starRating).toBeHidden()
  })
  test('Отображается финальный экран после заполнения всех вопросов', async ({
    page,
  }) => {
    await page.goto('default-2-questions')
    const starRating = page.getByTestId('star-rating')
    const ratingScale = page.getByTestId('rating-scale')
    const starItems = page.getByTestId('star-rating-item')
    const ratingScaleItems = page.getByTestId('rating-scale-item')
    const nextButton = page.getByText('Далее')
    const submitButton = page.getByText('Завершить')

    // Проходим первый вопрос

    await starItems.nth(0).click()
    await nextButton.click()

    await expect(starRating).toBeHidden()
    await expect(ratingScale).toBeVisible()
    await expect(ratingScaleItems).toHaveCount(5)
    await expect(submitButton).toBeEnabled()

    // Проходим второй вопрос
    await ratingScaleItems.nth(0).click()
    await expect(submitButton).toBeEnabled()
    await submitButton.click()

    // Проверяем финальный экран
    await expect(page.getByText('Опрос успешно пройден')).toBeVisible()
    await expect(page).toHaveScreenshot('default-2-questions-final-screen.png')
  })

  test('Отображает опрос с изменениями дизайна', async ({
    page,
  }) => {
    await page.goto('custom-design')
    await expect(await page.getByText('Звездный рейтинг')).toBeVisible()
    await expect(page).toHaveScreenshot('custom-design-screen.png')
    const starRating = page.getByTestId('star-rating')
    const ratingScale = page.getByTestId('rating-scale')
    const starItems = page.getByTestId('star-rating-item')
    const ratingScaleItems = page.getByTestId('rating-scale-item')
    const nextButton = page.getByText('Дем дальш')
    const submitButton = page.getByText('Финиш')

    // Проходим первый вопрос
    await starItems.nth(0).click()
    await nextButton.click()

    await expect(starRating).toBeHidden()
    await expect(ratingScale).toBeVisible()
    await expect(ratingScaleItems).toHaveCount(5)
    await expect(submitButton).toBeEnabled()

    // Делаем скриншот на втором экране
    await expect(page).toHaveScreenshot('custom-design-screen-2.png')

    // Проходим второй вопрос
    await ratingScaleItems.nth(0).click()
    await expect(submitButton).toBeEnabled()
    await submitButton.click()

    // Делаем скриншот на финальном экране
    await expect(page).toHaveScreenshot('custom-design-screen-final.png')
  })
  test('Отображает опрос с фоновым изображением', async ({
    page,
  }) => {
    await page.goto('default-with-bg-images')

    await expect(await page.getByText('Звездный рейтинг')).toBeVisible()
    await expect(page).toHaveScreenshot('default-with-bg-images-screen.png')
    await page.setViewportSize({ width: 375, height: 667 })
    await expect(page).toHaveScreenshot('default-with-bg-images-screen-mobile.png')
  })
})
