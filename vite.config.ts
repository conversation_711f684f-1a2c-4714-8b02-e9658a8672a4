import type { UserConfig } from 'vite'
import fs from 'node:fs'
import path from 'node:path'
import process from 'node:process'
import { sentryVitePlugin } from '@sentry/vite-plugin'
import vue from '@vitejs/plugin-vue'
import PreprocessorDirectives from 'unplugin-preprocessor-directives/vite'
import { defineConfig, loadEnv } from 'vite'

const env = loadEnv(process.env.NODE_ENV as string, process.cwd())

// Копируем только необходимые файлы в продакшене
function copyEssentialPublicFiles() {
  return {
    name: 'copy-essential-public-files',
    writeBundle(outputOptions: any) {
      if (process.env.NODE_ENV === 'production') {
        const outDir = outputOptions.dir || 'dist'

        // Копируем favicon.png в продакшене
        const faviconSrc = path.resolve('public', 'favicon.png')
        const faviconDest = path.resolve(outDir, 'favicon.png')

        if (fs.existsSync(faviconSrc)) {
          fs.copyFileSync(faviconSrc, faviconDest)
          console.log('✓ Копируем favicon.png в продакшене')
        }
      }
    },
  }
}

export default defineConfig(async () => {
  const plugins = [
    PreprocessorDirectives(),

    vue({
      template: {
        compilerOptions: {
          isCustomElement: (tag: string) => tag.startsWith('swiper-'),
        },
      },
    }),
    ...(env.VITE_USE_SENTRY === '1'
      ? [
          sentryVitePlugin({
            org: env.VITE_SENTRY_ORG,
            project: env.VITE_SENTRY_PROJECT,
            url: env.VITE_SENTRY_URL,
          }),
        ]
      : []),
    copyEssentialPublicFiles(),
  ]

  if (process.env.NODE_ENV === 'development') {
    const { default: vueDevTools } = await import('vite-plugin-vue-devtools')
    plugins.push(vueDevTools())
  }

  return {
    plugins,

    base: '/p/',

    resolve: {
      alias: {
        '@': path.resolve(__dirname, './src'),
        '@components': path.resolve(__dirname, './src/components'),
        '@entities': path.resolve(__dirname, './src/entities'),
        '@widgets': path.resolve(__dirname, './src/widgets'),
        '@app': path.resolve(__dirname, './src/app'),
        '@shared': path.resolve(__dirname, './src/shared'),
        '@features': path.resolve(__dirname, './src/features'),
        ...(env.VITE_USE_SENTRY !== '1'
          ? { '@sentry/vue': path.resolve(__dirname, './src/shared/stubs/sentry-stub.js') }
          : {}),
      },
    },

    css: {
      preprocessorOptions: {
        scss: {
          api: 'modern-compiler',
        },
      },
    },

    // Устанавливаем publicDir в зависимости от окружения
    publicDir: process.env.NODE_ENV === 'production' ? false : 'public',

    build: {
      sourcemap: process.env.NODE_ENV && !process.env.NODE_ENV.includes('production'),
      rollupOptions: {
        external: process.env.NODE_ENV === 'production' ? ['msw', 'msw/browser', 'msw/node'] : [],
      },
    },
  } satisfies UserConfig
})
