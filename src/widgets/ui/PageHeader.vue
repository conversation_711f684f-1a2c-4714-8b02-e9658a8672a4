<script setup>
import { usePollStore } from '@entities/poll/model/store'
import { computed } from 'vue'

const props = defineProps({
  descriptionHtml: {
    type: String,
    default: '',
  },
  subdescriptionHtml: {
    type: String,
    default: '',
  },
  unrequiredText: {
    type: String,
    default: '',
  },
  type: {
    type: String,
    default: 'default',
  },
})

const pollStore = usePollStore()
const isOldTitleFormat = pollStore.isOldTitleFormat
const isFontSizeMustChange = pollStore.isFontSizeMustChange

const unrequiredTextClasses = computed(() => {
  return {
    'page-header__unrequired': true,
    'page-header__unrequired--no-margin': !props.descriptionHtml && !props.subdescriptionHtml,
  }
})
const htmlRegex = /<[^>]*>|&nbsp;/g
/**
 * Функция для рендеринга текста описания вопроса
 * @param {string} description - HTML-строка с описанием
 * @returns {string | null} - HTML-строка с описанием или null, если описание не передано
 */
function renderDescription(description) {
  // Если описание не передано, то не рендерим
  if (!description)
    return null

  // Преобразуем описание в строку и убираем пробельные символы
  const trimmedDesc = description.toString().trim()

  // @NOTE: Очень специфичная логика, которая позволяет не рендерить описание,
  // если оно состоит только из одной точки
  // Некоторые компании используют одну точку как описание по умолчанию
  // для первого вопроса в рассылке. Из-за обратной совместимости приходится
  // обрабатывать такое поведение
  const parsedDescriptionHtml = trimmedDesc.replace(htmlRegex, '')
  return parsedDescriptionHtml !== '.' ? description : null
}

const descriptionClasses = computed(() => {
  return {
    'page-header__description': true,
    'page-header__description--five-second-test': props.type === 'five-second-test',
  }
})
</script>

<template>
  <div
    class="page-header"
    :class="{
      'page-header_old': isOldTitleFormat,
      'page-header_font-size-must-change': isFontSizeMustChange,
      'page-header_five-second-test': props.type === 'five-second-test',
    }"
  >
    <h2 v-if="descriptionHtml" :class="descriptionClasses" v-html="renderDescription(descriptionHtml)" />
    <p v-if="subdescriptionHtml" class="page-header__subdescription" v-html="subdescriptionHtml" />
    <p v-if="unrequiredText" :class="unrequiredTextClasses">
      {{ unrequiredText }}
    </p>
  </div>
</template>

<style scoped>
.page-header {
  width: 100%;
  max-width: 680px;
  padding: 0 15px;
  font-size: var(--fqz-poll-title-font-size, 24px);
}

.page-header__description {
  font-weight: bold;
  line-height: 1.15;
  margin-bottom: 0;
  font-weight: 400;
  font-size: var(--fqz-poll-title-font-size, 24px);
}

.page-header__subdescription {
  margin-top: 15px;
  line-height: 1.1;
  color: var(--poll-text-on-bg);
  font-weight: 400;
  font-size: 16px;
}
.page-header_old :deep(p) {
  text-align: center !important;
}

.page-header__unrequired {
  color: var(--poll-text-on-bg);
  font-size: 12px;
  line-height: 1.1;
  font-weight: 400;
  margin-top: 15px;
  text-align: center;
}

.page-header__unrequired--no-margin {
  margin-top: 0;
}

@media screen and (max-width: 679px) {
  .page-header {
    font-size: 21px;
  }

  .page-header__description:not(.page-header__description--five-second-test) {
    font-size: 19px;
  }

  .page-header__description:not(.page-header__description--five-second-test) :deep(span) {
    font-size: 19px !important;
  }

  .page-header__subdescription {
    font-size: 14px;
  }

  .page-header__subdescription :deep(span) {
    font-size: 14px !important;
  }

  .page-header__unrequired {
    font-size: 12px;
  }

  .page-header__unrequired :deep(span) {
    font-size: 12px !important;
  }
}
</style>
