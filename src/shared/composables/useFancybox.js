/**
 * @file useFancybox.js
 * @description Композабл для управления галереями Fancybox в приложениях Vue 3.
 */

import { Fancybox } from '@fancyapps/ui'
import { useTranslationsStore } from '@shared/store/translationsStore'
import { computed, onMounted, ref, toValue, watch } from 'vue'
import { ALLOWED_BUT_NOT_SUPPORTED } from '../constants/files'
import { getFileTypeFromFilename } from '../helpers/files'
import { getUniqueId } from '../helpers/general'

/**
 * @typedef {object} Gallery
 * @property {Array} items - Массив элементов галереи
 * @property {object} options - Опции Fancybox для галереи
 */

/**
 * Объект, содержащий все галереи.
 * Ключ - это имя группы галереи. По умолчанию - 'global'.
 * @type {import('vue').Ref<{[key: string]: Gallery}>}
 */
const galleries = ref({
  global: { items: [], options: {} },
})

/**
 * @type {Record<string, string>}
 */
const formatToMime = {
  // Видео форматы
  mp4: 'video/mp4',
  webm: 'video/webm',
  avi: 'video/x-msvideo',
  mov: ['video/quicktime', 'video/x-quicktime', 'video/mp4'],
  wmv: 'video/x-ms-wmv',
  flv: 'video/x-flv',
  mkv: 'video/x-matroska',
  mpeg: 'video/mpeg',
  ogv: 'video/ogg',

  // Аудио форматы
  mp3: 'audio/mpeg',
  wav: 'audio/wav',
  ogg: 'audio/ogg',
  aac: 'audio/aac',
  m4a: 'audio/mp4',
  flac: 'audio/flac',
  wma: 'audio/x-ms-wma',
  opus: 'audio/opus',
}

// List of formats that are known to be unsupported in browsers
const unsupportedFormats = [
  ...ALLOWED_BUT_NOT_SUPPORTED.image,
  ...ALLOWED_BUT_NOT_SUPPORTED.audio,
]

const translations = ref(null)
const selectedLanguage = ref(null)

const extensionRegex = /\.\w+$/

/**
 * Проверяет, поддерживается ли данный формат файла браузером.
 * @param {string} src - URL-адрес источника файла
 * @returns {boolean} True, если формат поддерживается, иначе false
 */
function isFormatSupported(src) {
  const hasExtension = extensionRegex.test(src)
  if (!hasExtension) {
    return true
  }

  const format = src.split('.').pop().toLowerCase()

  // Check if format is in the unsupported list
  if (unsupportedFormats.includes(format)) {
    return false
  }

  const fileType = getFileTypeFromFilename(src)

  // For audio and image formats, we assume they are supported if not in unsupportedFormats
  // and not in formatToMime (which contains only audio/video formats)
  if (fileType === 'audio' || fileType === 'image') {
    return true
  }

  // For video formats
  if (Object.keys(formatToMime).includes(format)) {
    const video = document.createElement('video')
    const mime = formatToMime[format]

    if (Array.isArray(mime)) {
      return mime.some(m => video.canPlayType(m))
    }

    return video.canPlayType(mime)
  }

  return false
}

/**
 * Генерирует HTML-шаблон для видео элементов.
 * @param {object} opts - Опции для видео шаблона
 * @param {string} opts.src - URL-адрес источника видео
 * @param {string} [opts.poster] - URL-адрес изображения постера для видео
 * @returns {string} HTML-шаблон для видео элемента
 */
function getVideoTpl(opts) {
  const format = opts.src.split('.').pop()
  const mime = formatToMime[format]
  return `<video class="fancybox__html5video" playsinline controls controlsList="nodownload" poster="${opts.poster || ''}" preload="auto"><source src="${opts.src}" type="${mime}" />Извините, ваш браузер не поддерживает встроенные видео.</video>`
}

/**
 * Генерирует HTML-шаблон для неподдерживаемых форматов.
 * @param {object} opts - Опции для шаблона
 * @param {string} opts.src - URL-адрес файла
 * @returns {import('vue').ComputedRef<string>} Вычисляемый HTML-шаблон для неподдерживаемых форматов
 */
function getFormatNotSupportedTpl(opts) {
  const src = opts.src
  let defaultText = 'Не удается воспроизвести файл. Формат не поддерживается'
  let defaultLinkText = 'Скачать'

  const englishText = 'The file cannot be played. Format not supported.'
  const englishLinkText = 'Download'

  /**
   * Генерирует HTML-шаблон для неподдерживаемых форматов.
   * Обернуто в computed, чтобы можно было использовать переводы.
   */
  return computed(() => {
    const t = translations.value
    const lang = selectedLanguage.value
    const shortCode = lang?.shortCode

    // Если язык не русский, то используем английский текст
    if (shortCode && shortCode !== 'ru') {
      defaultText = englishText
      defaultLinkText = englishLinkText
    }
    return t
      ? `
  <div class="custom-fancybox__not-supported">
    <div class="custom-fancybox__not-supported-inner">
      <p class="custom-fancybox__not-supported-title">${defaultText}</p>
      <a class="custom-fancybox__not-supported-link" href="${src}" download>
        <svg class="custom-fancybox__not-supported-link-icon" width="13" height="11" viewBox="0 0 13 11" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M11.5 10H1.5M6.5 1V7M6.5 7L8.5 5M6.5 7L4.5 5" stroke="#73808D" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
          <span class="custom-fancybox__not-supported-link-text">${defaultLinkText}</span>
        </a>
      </div>
    </div>
`
      : ''
  })
}

/**
 * Генерирует HTML для видео и аудио элементов на основе поддержки формата.
 * @param {object} opts - Опции для генерации HTML
 * @param {string} opts.src - URL-адрес источника медиа
 * @returns {string|import('vue').ComputedRef<string>} HTML для медиа элемента
 */
function getVideoAndAudioHtml(opts) {
  return isFormatSupported(opts.src) ? getVideoTpl(opts) : getFormatNotSupportedTpl(opts)
}

/**
 * @type {Record<string, (opts: object) => object>}
 */
const itemOptionsMap = {
  picture: (opts) => {
    // Check if the image format is supported
    const isSupported = isFormatSupported(opts.src)

    if (!isSupported) {
      return {
        ...opts,
        type: 'html',
        customClass: 'has-unsupported-format',
        html: toValue(getFormatNotSupportedTpl(opts)),
      }
    }

    return {
      ...opts,
      type: 'image',
      backdropClick: () => {
        return 'close'
      },
      options: {
        caption: slide => slide.type,
      },
    }
  },
  image: (opts) => {
    // Check if the image format is supported
    const isSupported = isFormatSupported(opts.src)

    if (!isSupported) {
      return {
        ...opts,
        type: 'html',
        customClass: 'has-unsupported-format',
        html: toValue(getFormatNotSupportedTpl(opts)),
      }
    }

    return {
      ...opts,
      type: 'image',
      backdropClick: () => {
        return 'close'
      },
      options: {
        caption: slide => slide.type,
      },
    }
  },
  video: (opts) => {
    const isYoutube = opts.src.includes('youtube.')

    const notSupportedOptions = {
      ...opts,
      type: 'html5video',
      customClass: 'has-html5video',
      html: toValue(getVideoAndAudioHtml(opts)),
    }

    const isSupported = isFormatSupported(opts.src)

    const supportedOptions = {
      ...opts,
      type: 'html5video',
      customClass: 'has-html5video',
      Html: {
        videoAutoplay: true,
      },
    }

    const finalOptions = isSupported ? supportedOptions : notSupportedOptions

    const html5VideoOptions = finalOptions

    const youtubeOptions = {
      ...opts,
      type: 'youtube',
      options: {
        youtube: {
          autoplay: 1,
        },
      },
    }

    return isYoutube ? youtubeOptions : html5VideoOptions
  },
  audio: (opts) => {
    const isSupported = isFormatSupported(opts.src)

    const notSupportedOptions = {
      ...opts,
      type: 'html5video',
      customClass: 'has-html5video',
      html: toValue(getFormatNotSupportedTpl(opts)),
    }

    const supportedOptions = {
      ...opts,
      type: 'html5video',
      customClass: 'has-html5video custom-fancybox-audio',
      Html: {
        videoAutoplay: true,
        videoTpl: '<video class="fancybox__html5video" playsinline controls poster="{{poster}}"><source src="{{src}}" type="{{format}}" />Извините, ваш браузер не поддерживает встроенные видео.</video>',
      },
    }

    const finalOptions = isSupported ? supportedOptions : notSupportedOptions

    return finalOptions
  },
}

/**
 * @type {object} Опции по умолчанию для Fancybox
 */
const defaultOptions = {
  Thumbs: false,
  Slideshow: true,
  mainClass: 'custom-fancybox',
  mobile: { clickOutside: 'close' },
  backdropClick: () => {
    return 'close'
  },
  Toolbar: {
    enabled: true,
  },
  Html: {
    videoAutoplay: true,
    videoTpl: '<video class="fancybox__html5video" playsinline controls disablepictureinpicture poster="{{poster}}"><source src="{{src}}" type="{{format}}" />Извините, ваш браузер не поддерживает встроенные видео.</video>',
  },
  Carousel: {
    Navigation: {
      nextTpl: '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M15.4 12.97l-2.68 2.72 1.34 1.38L19 12l-4.94-5.07-1.34 1.38 2.68 2.72H5v1.94z"></path></svg>',
      prevTpl: '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M11.28 15.7l-1.34 1.37L5 12l4.94-5.07 1.34 1.38-2.68 2.72H19v1.94H8.6z"></path></svg>',
    },
  },
  on: {
    'close': () => {
      const focusedElement = document.activeElement
      if (focusedElement) {
        focusedElement.blur()
      }
    },
    'Carousel.click': (instance, fb, e) => {
      const isFancyboxSlideClicked = e.target.closest('.fancybox__slide')
      const isFancyboxContentClicked = e.target.closest('.fancybox__content')
      if (isFancyboxSlideClicked && !isFancyboxContentClicked) {
        if (instance && typeof instance.close === 'function') {
          instance.close()
        }
      }
    },
    'Carousel.selectSlide': (instance) => {
      if (!instance.container) {
        return
      }
      const videoEl = instance.container.querySelector?.('video')
      if (videoEl && !videoEl.hasAttribute('data-foquz-html5video-replaced')) {
        // создаем новый видео элемент
        const clonedVideoEl = videoEl.cloneNode(true)
        // заменяем старый видео элемент новым
        videoEl.parentNode.replaceChild(clonedVideoEl, videoEl)
        clonedVideoEl.setAttribute('data-foquz-html5video-replaced', '')
      }
    },
  },
}

/**
 * Композабл для управления галереями Fancybox.
 * @param {string} [group] - Имя группы галереи
 * @param {object} [slideOptions] - Пользовательские опции для слайдов Fancybox
 * @returns {object} Объект, содержащий методы для управления галереей
 */
export function useFancybox(group = 'global', slideOptions = defaultOptions) {
  const groupName = group || 'global'
  const addedItem = ref(null)
  const translationsStore = useTranslationsStore()
  const t = translationsStore.t
  const l10n = ref({})

  onMounted(() => {
    translations.value = t
    selectedLanguage.value = translationsStore.selectedLang
  })

  watch(() => translationsStore.selectedLang, (_) => {
    l10n.value = {
      CLOSE: toValue(t('Закрыть')),
      PREV: toValue(t('Предыдущий')),
      NEXT: toValue(t('Следующий')),
      DOWNLOAD: toValue(t('Скачать')),
      TOGGLE_SLIDESHOW: toValue(t('Слайд-шоу')),
      TOGGLE_FULLSCREEN: toValue(t('Полноэкранный режим')),
      TOGGLE_ZOOM: toValue(t('Увеличить/Уменьшить')),
      TOGGLEZOOM: toValue(t('Увеличить/Уменьшить')),
      TOGGLE1TO1: toValue(t('Увеличить/Уменьшить')),
      ITERATEZOOM: toValue(t('Увеличить/Уменьшить')),
    }
  }, { deep: true, immediate: true })

  if (!galleries.value[groupName]) {
    galleries.value[groupName] = { items: [], options: slideOptions }
  }
  else {
    galleries.value[groupName].options = { ...galleries.value[groupName].options, ...slideOptions }
  }

  /**
   * Показывает галерею Fancybox.
   * @param {number} [startIndex] - Индекс элемента, с которого начинается галерея
   */
  const show = (startIndex = 0) => {
    const currentGallery = galleries.value[groupName]

    const items = currentGallery.items.map(i => toValue(i)).filter(i => toValue(i.src))

    const index = items.findIndex((item) => {
      const src = toValue(item.src)
      const addedSrc = toValue(addedItem.value)?.src
      const id = toValue(item.id)
      const addedId = toValue(addedItem.value)?.id

      if (src && addedSrc) {
        return src === addedSrc
      }
      else if (id && addedId) {
        return id === addedId
      }
      return false
    })
    startIndex = index

    const galleryOptions = {
      ...currentGallery.options,
      startIndex,
      l10n: l10n.value,
    }

    Fancybox.show(
      currentGallery.items.length > 0
        ? items
        : document.querySelectorAll(`[data-fancybox="${groupName}"]`),
      galleryOptions,
    )
  }

  /**
   * Добавляет элемент в галерею.
   * @param {object} item - Элемент для добавления в галерею
   * @param {string} item.src - URL-адрес источника элемента
   * @param {string} item.type - Тип элемента (например, 'picture', 'video', 'audio')
   * @param {string} item.id - Идентификатор элемента
   */
  const addItem = (item) => {
    const itemId = toValue(item.id)

    const items = galleries.value[groupName].items
    // если элемент с таким id уже есть, то ничего не делаем
    if (itemId && items.find(i => toValue(i)?.id === itemId)) {
      return
    }

    const computedItem = computed(() => {
      const src = toValue(item.src)
      const type = toValue(item.type)
      const id = toValue(item.id) || getUniqueId(groupName)

      if (!src) {
        return { id }
      }

      const itemType = itemOptionsMap[type] || itemOptionsMap.picture
      return itemType({
        ...item,
        src,
        id,
        type,
      })
    })

    galleries.value[groupName].items.push(computedItem)

    addedItem.value = computedItem
  }

  return {
    /**
     * Показывает галерею Fancybox, начиная с последнего добавленного элемента.
     */
    show: () => {
      const currentGallery = galleries.value[groupName]
      const items = currentGallery.items.map(i => toValue(i)).filter(i => toValue(i.src))
      const index = items.findIndex((item) => {
        const src = toValue(item.src)
        const addedSrc = toValue(addedItem.value?.src)
        const id = toValue(item.id)
        const addedId = toValue(addedItem.value?.id)

        if (src && addedSrc) {
          return src === addedSrc
        }
        else if (id && addedId) {
          return id === addedId
        }
        return false
      })
      show(index)
    },
    /**
     * Удаляет последний добавленный элемент из галереи.
     */
    removeItem: () => {
      const currentGallery = galleries.value[groupName]
      const items = currentGallery.items.map(i => toValue(i))
      const index = items.findIndex((item) => {
        const src = toValue(item.src)
        const addedSrc = toValue(addedItem.value)?.src
        const id = toValue(item.id)
        const addedId = toValue(addedItem.value)?.id

        if (src && addedSrc) {
          return src === addedSrc
        }
        else if (id && addedId) {
          return id === addedId
        }
        return false
      })

      if (index === -1) {
        return
      }

      currentGallery.items.splice(index, 1)
    },
    addItem,
    /**
     * Проверяет, поддерживается ли формат файла браузером.
     */
    isFormatSupported,
  }
}
