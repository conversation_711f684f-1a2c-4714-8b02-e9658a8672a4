.has-youtube .fancybox__content,
.has-vimeo .fancybox__content,
.has-html5video .fancybox__content {
  width: 1190px !important;
  height: 768px !important;
}

.fancybox__container:not(.is-compact) .fancybox__slide.has-close-btn {
  padding-top: 60px !important;
  padding-bottom: 40px !important;
}

.custom-fancybox:not(.is-compact) .fancybox__slide.has-html5video {
  padding-top: 50px !important;
  padding-bottom: 30px !important;
}

.fancybox__html5video:focus {
  outline: none !important;
}

.custom-fancybox-audio .fancybox__content {
  background-color: rgba(242, 243, 245, 1) !important;
  position: relative;
  overflow: hidden;
}

.custom-fancybox-audio .fancybox__content:before {
  content: '';
  position: absolute;
  top: calc(50% - 20px);
  left: 50%;
  transform: translate(-50%, -50%);
  width: 200px;
  height: 200px;
  background-color: rgba(242, 243, 245, 1);
  background-image: url('@/shared/assets/audio-player-icon.svg');
  background-repeat: no-repeat;
  background-position: center;
  z-index: -1;
  pointer-events: none;
}

.custom-fancybox__not-supported {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 25px;
  height: 90vh !important;
  width: 100%;
  max-width: 1190px !important;
  max-height: 768px !important;
  background-color: rgba(242, 243, 245, 1) !important;
}

.custom-fancybox__not-supported-title {
  font-size: 16px;
  font-weight: 400;
  line-height: 1.1;
  color: black;
  text-align: center;
}

.custom-fancybox__not-supported-link {
  text-decoration: none;
  color: #000;
  background-color: rgba(255, 255, 255, 1);
  transition: all 0.3s;
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 12px;
  line-height: 1.1;
  border-radius: 18px;
  transition: opacity 0.3s;
  height: 36px;
  padding-left: 17px;
  padding-right: 15px;
}

.custom-fancybox__not-supported-link:hover {
  opacity: 0.8;
}

.custom-fancybox__not-supported-inner {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 25px;
}

.custom-fancybox .fancybox__nav .f-button svg {
  stroke: none;
  fill: white;
  stroke-width: 0;
}

@media screen and (max-width: 679px) {
  .custom-fancybox-audio .fancybox__content {
    aspect-ratio: 10/10 !important;
  }

  .custom-fancybox__not-supported-inner {
    max-width: 280px;
  }

  .custom-fancybox__not-supported-title {
    font-size: 14px;
  }
  .has-youtube .fancybox__content,
  .has-vimeo .fancybox__content,
  .has-html5video .fancybox__content:not(.custom-fancybox__not-supported) {
    height: auto !important;
  }

  .is-compact .fancybox__nav {
    display: none !important;
  }
}
