import { RATING_QUESTION, STARS_QUESTION } from '@entities/question/model/types'

// @TODO: удалить. Возможно, этот код не нужен

/**
 * Prepares base question preview data
 * @param {object} previewData - Raw preview data
 * @returns {object} Prepared base question data
 */
export function prepareBaseQuestionPreviewData(previewData) {
  if (!previewData)
    return {}

  return {
    ...previewData,
    // Basic fields
    id: previewData.id,
    type: previewData.type,
    name: previewData.name || previewData.title,
    description: previewData.description,
    description_html: previewData.description_html || previewData.descriptionHtml,
    subdescription: previewData.subdescription || previewData.subDescription || previewData.sub_description,

    // Question settings
    isRequired: previewData.isRequired,
    question_id: previewData.question_id || previewData.id,
    showNumber: previewData.showNumber !== false,

    // Text field parameters if present
    textFieldParam: previewData.textFieldParam || previewData.textfield_param || {},
    placeholderText: previewData.placeholderText || previewData.placeholder,

    // Comment settings if present
    comment_enabled: previewData.commentEnabled || previewData.comment_enabled,
    comment_required: previewData.commentRequired || previewData.comment_required,
    comment_label: previewData.commentLabel || previewData.comment_label,

    // Clarifying question settings if present
    clarifyingQuestionRequired: previewData.clarifyingQuestionRequired,
    clarifyingQuestionEnabled: previewData.clarifyingQuestionEnabled,

  }
}

/**
 * Prepares stars question preview data
 * @param {object} previewData - Raw preview data
 * @returns {object} Prepared stars question data
 */
export function prepareStarsQuestionPreviewData(previewData) {
  if (!previewData)
    return {}

  return {
    ...prepareBaseQuestionPreviewData(previewData),

    // Stars configuration
    starRatingOptions: {
      color: previewData.starRatingOptions?.color || 'rgb(248, 205, 28)',
      size: previewData.starRatingOptions?.size || 'md',
      count: previewData.starRatingOptions?.count || 5,
      labelsArray: previewData.starRatingOptions?.labelsArray || [],
      extra_question_rate_from: previewData.starRatingOptions?.extra_question_rate_from || -Infinity,
      extra_question_rate_to: previewData.starRatingOptions?.extra_question_rate_to || Infinity,
    },

    stars: previewData.stars || 5,
    // Skip options
    skip: previewData.skip ?? 0,
    skip_text: previewData.skip_text || '',

    // Display options
    show_labels: previewData.show_labels ?? 0,
    show_numbers: previewData.show_numbers ?? 0,

    // Comment settings
    comment_enabled: previewData.isHaveComment ? 1 : 0,
    comment_required: previewData.comment_required ?? 0,
    comment_label: previewData.comment_label || '',
    textFieldParam: {
      min: previewData.textFieldParam?.min ?? 0,
      max: previewData.textFieldParam?.max ?? 250,
    },
    placeholderText: previewData.placeholderText || '',

    // Gallery settings
    enableGallery: previewData.enableGallery ?? 0,
    gallery: prepareGalleryPreviewData(previewData),

    // Variants and detail answers
    answerText: previewData.answerText,
    variants: prepareVariantsPreviewData(previewData),
    detail_answers: previewData.detail_answers || [],
    variantsType: previewData.variantsType,
    isHaveCustomField: previewData.isHaveCustomField ?? false,
    dropdownVariants: previewData.dropdownVariants ?? false,
    self_variant_text: previewData.self_variant_text || '',
    self_variant_nothing: previewData.self_variant_nothing ? 1 : 0,
    comment: { ...prepareCommentPreviewData(previewData) },
    forAllRates: previewData.forAllRates,
  }
}

/**
 * Подготавливает данные предпросмотра вопроса
 * Наше приложение может работать в режиме предпросмотра, при котором данные
 * вопроса приходят из внешнего приложения. В этом случае нам нужно подготовить
 * данные вопроса в соответствии с нашими моделями
 * @param {object} previewData - Данные вопроса
 * @returns {object} Подготовленные данные вопроса
 */
export function prepareQuestionPreviewData(previewData) {
  if (!previewData)
    return {}

  switch (previewData.type) {
    case STARS_QUESTION:
      return prepareStarsQuestionPreviewData(previewData)
    case RATING_QUESTION:
      return prepareRatingScalePreviewData(previewData)
    default:
      return prepareBaseQuestionPreviewData(previewData)
  }
}

export function prepareGalleryPreviewData(previewData) {
  if (!previewData?.gallery)
    return []

  return previewData.gallery.map(item => ({
    id: item.id || Math.random(),
    persistentId: item.persistentId,
    src: item.url || '',
    url: item.url || '',
    preview: item.preview || '',
    poster: item.poster || '',
    description: item.description || '',
    position: item.position || 0,
  }))
}

export function prepareVariantsPreviewData(previewData) {
  if (!previewData?.variants)
    return []

  return previewData.variants.map(item => ({
    id: item.id,
    variant: item.value || '',
    value: item.value || '',
    points: item.points || '',
    file_id: item.file_id || null,
    position: item.position || item.id || 0,
    type: item.type || 0,
    is_deleted: 0,
  }))
}

export function prepareCommentPreviewData(previewData) {
  if (!previewData)
    return {}

  return {
    enabled: previewData.isHaveComment || false,
    required: previewData.comment_required || false,
    title: previewData.comment_label || '',
    placeholderText: previewData.placeholderText || '',
    textFieldParam: {
      min: previewData.textFieldParam?.min || 0,
      max: previewData.textFieldParam?.max || 1000,
    },
    value: previewData.comment || '',
  }
}

/**
 * Prepares rating scale question preview data
 * @param {object} previewData - Raw preview data
 * @returns {object} Prepared rating scale question data
 */
export function prepareRatingScalePreviewData(previewData) {
  if (!previewData)
    return {}

  return {
    ...prepareBaseQuestionPreviewData(previewData),

    // Scale configuration
    starRatingOptions: {
      color: previewData.starRatingOptions?.color || 'rgb(63, 101, 241)',
      size: previewData.starRatingOptions?.size || 'md',
      count: previewData.starRatingOptions?.count || 5,
      labelsArray: previewData.starRatingOptions?.labelsArray || [],
      extra_question_rate_from: previewData.starRatingOptions?.extra_question_rate_from || -Infinity,
      extra_question_rate_to: previewData.starRatingOptions?.extra_question_rate_to || Infinity,
    },

    stars: previewData.stars || 5,
    // Skip options
    skip: previewData.skip ?? 0,
    skip_text: previewData.skip_text || '',

    // Gallery settings
    enableGallery: previewData.enableGallery ?? 0,
    gallery: prepareGalleryPreviewData(previewData),

    // Variants and detail answers
    answerText: previewData.answerText,
    variants: prepareVariantsPreviewData(previewData),
    variantsType: previewData.variantsType,
    isHaveCustomField: previewData.isHaveCustomField ?? false,

    // Text field parameters
    textFieldParam: {
      min: previewData.textFieldParam?.min ?? 0,
      max: previewData.textFieldParam?.max ?? 250,
    },
    placeholderText: previewData.placeholderText || '',
    self_variant_text: previewData.self_variant_text || '',
    forAllRates: previewData.forAllRates,
    comment: prepareCommentPreviewData(previewData),
  }
}
