<script setup>
import { useTabletStore } from '@shared/store/useTabletStore'
import Check from '@shared/ui/Check.vue'
import FormError from '@shared/ui/FormError.vue'
import FormGroup from '@shared/ui/FormGroup.vue'
import Gallery from '@shared/ui/Gallery/Gallery.vue'
import SlideTransition from '@shared/ui/SlideTransition.vue'
import StarRating from '@shared/ui/StarRating.vue'
import Textarea from '@shared/ui/Textarea.vue'
import { computed, toValue } from 'vue'
import Variants from './Variants.vue'

const props = defineProps({
  question: {
    type: Object,
    required: true,
  },
})

const {
  showLabels,
  showNumbers,
  labels,
  stars,
  id,
  count,
  size,
  color,
  isCanChange,
  skipped,
  err,
  hasComment,
  commentController,
  isClarifyingQuestionVisible,
  variantsController,
  skip,
  skipText,
  enableGallery,
  galleryController,
} = props.question

const tabletStore = useTabletStore()

const {
  value: commentValue,
  placeholderText: commentPlaceholderText,
  minLength: commentMinLength,
  maxLength: commentMaxLength,
  error: commentError,
  title: commentTitle,
} = commentController

const activeLabel = computed(() => {
  const currentStars = toValue(stars)
  if (currentStars) {
    return labels.value[currentStars - 1]
  }
  return ''
})
</script>

<template>
  <div>
    <form :id="`answer-form-${id}`">
      <div class="survey-questions__wrapper_pre">
        <div v-if="enableGallery" class="gallery-container">
          <Gallery
            :gallery="galleryController.gallery.value"
            :selectable="false"
            :inactive="skipped"
            :group-id="question.questionId"
            type="default"
          />
        </div>

        <div class="star-rating-container">
          <StarRating
            v-model="stars" :max="count" :size="size" :color="color" :is-can-change="isCanChange"
            :thin="tabletStore.isTabletMode" :show-labels="showLabels" :show-numbers="showNumbers" :labels="labels" :inactive="skipped"
          />
          <TransitionGroup name="fade-up" class="fc-star-rating__message-container" tag="div">
            <FormError v-if="err" :key="1" :error="err" class="question-rating-error" />
            <div v-if="activeLabel && !showLabels" :key="2" class="fc-star-rating__message" data-testid="active-label">
              {{ activeLabel }}
            </div>
          </TransitionGroup>
        </div>

        <SlideTransition>
          <div
            v-if="hasComment && !skipped && commentController.enabled"
            data-testid="comment"
          >
            <FormGroup
              custom-class="survey-questions__comment-form-group"
              :label="commentTitle"
              :error="commentError"
            >
              <Textarea
                v-model="commentValue"
                :maxlength="commentMaxLength"
                :minlength="commentMinLength"
                :placeholder="commentPlaceholderText"
                :is-invalid="commentError"
              />
            </FormGroup>
          </div>
        </SlideTransition>

        <SlideTransition>
          <div v-if="isClarifyingQuestionVisible">
            <Variants
              :variants-controller="variantsController"
              custom-class="survey-questions__variants-form-group"
            />
          </div>
        </SlideTransition>

        <div v-if="skip" class="skip-container">
          <Check v-model="skipped" :label="skipText" />
        </div>
      </div>
    </form>
  </div>
</template>

<style scoped>
.star-rating-container {
  display: flex;
  justify-content: center;
  flex-direction: column;
  align-items: center;
  padding-top: 1px;
}

.survey-questions__variants-form-group {
  padding-top: 30px;
}

.gallery-container {
  margin-bottom: 30px;
  margin-top: -10px;
}

.survey-questions__comment-form-group {
  padding-top: 30px;
}

.fc-star-rating__message-container {
  margin-top: 20px;
  text-align: center;
  position: relative;
}

.fc-star-rating__message-container:empty {
  display: none;
}

:global(.fc-star-rating__message-container .fade-up-leave-active) {
  position: absolute !important;
  width: 100% !important;
  left: 0 !important;
}

.fc-star-rating__message {
  font-size: var(--fqz-poll-font-size, 13px);
  font-weight: 600;
  line-height: 1.1;
}

.fc-star-rating__message--error {
  color: #dc3545;
}

.skip-container {
  margin-top: 30px;
  text-align: center;
}

.skip-container label {
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.skip-container input[type='checkbox'] {
  margin-right: 8px;
}

@media (max-width: 679px) {
  .fc-star-rating__message {
    font-size: 14px;
  }
}
</style>
