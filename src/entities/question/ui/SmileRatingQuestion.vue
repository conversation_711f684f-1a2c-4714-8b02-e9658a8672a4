<script setup>
import { useSimplifiedStore } from '@shared/store/useSimplifiedStore'
import Check from '@shared/ui/Check.vue'
import FormError from '@shared/ui/FormError.vue'
import FormGroup from '@shared/ui/FormGroup.vue'
import Gallery from '@shared/ui/Gallery/Gallery.vue'
import SlideTransition from '@shared/ui/SlideTransition.vue'
import SmileRating from '@shared/ui/SmileRating.vue'
import Textarea from '@shared/ui/Textarea.vue'
import { computed } from 'vue'
import Variants from './Variants.vue'

const props = defineProps({
  question: {
    type: Object,
    required: true,
  },
})

const {
  id,
  value,
  smiles,
  showLabels,
  labels,
  skipped,
  err,
  commentEnabled,
  commentController,
  skip,
  skipText,
  smileType,
  enableGallery,
  galleryController,
  questionId,
} = props.question

const { title, error, value: commentValue, maxLength, minLength, placeholderText } = commentController

const simplifiedStore = useSimplifiedStore()

const activeLabel = computed(() => {
  const smile = smiles.value.find(s => s.id === value.value)
  return smile ? smile.label : ''
})

const hasSelectedItem = computed(() => {
  if (smiles.value.length > 0) {
    const smile = smiles.value.find(s => s.id === value.value)
    return !!smile
  }

  return false
})

const smileRatingContainerClasses = computed(() => ({
  'smile-rating-container': true,
  'smile-rating-container--simplified': simplifiedStore.isSimplifiedMode,
  'smile-rating-container--has-selected-item': hasSelectedItem.value,
  'smile-rating-container--has-label': !!activeLabel.value,
  'smile-rating-container--like': smileType.value === 'like',
}))
</script>

<template>
  <div>
    <form :id="`answer-form-${id}`">
      <div class="survey-questions__wrapper_pre" :class="{ 'with-comment': commentEnabled }">
        <div v-if="enableGallery" class="gallery-container">
          <Gallery
            :gallery="galleryController.gallery.value"
            :selectable="false"
            :inactive="skipped"
            type="default"
            :group-id="questionId"
          />
        </div>

        <div :class="smileRatingContainerClasses">
          <SmileRating
            v-if="smiles.length"
            v-model="value"
            :smiles="smiles"
            :show-labels="showLabels"
            :labels="labels"
            :inactive="skipped"
            :type="smileType"
          />
        </div>

        <TransitionGroup
          name="fade-up"
          class="smile-rating__message-container"
          tag="div"
        >
          <FormError v-if="err" :key="1" :error="err" class="smile-rating-error" />
          <div v-if="!!activeLabel && !showLabels" :key="2" class="smile-rating__message" data-testid="smile-rating-message">
            {{ activeLabel }}
          </div>
        </TransitionGroup>

        <SlideTransition>
          <div v-if="question.variantsController.enabled.value && !skipped">
            <div class="survey-questions__variants-form-group-container">
              <Variants
                :variants-controller="question.variantsController"
                media-view="gallery"
                custom-class="survey-questions__variants-form-group"
              />
            </div>
          </div>
        </SlideTransition>

        <SlideTransition>
          <div v-if="commentEnabled && !skipped">
            <FormGroup
              custom-class="survey-questions__comment-form-group"
              :label="title"
              :error="error"
              data-testid="comment"
            >
              <Textarea
                v-model="commentValue"
                :maxlength="maxLength"
                :minlength="minLength"
                :placeholder="placeholderText"
                :is-invalid="error"
              />
            </FormGroup>
          </div>
        </SlideTransition>

        <div v-if="skip" class="skip-container">
          <Check v-model="skipped" :label="skipText" />
        </div>
      </div>
    </form>
  </div>
</template>

<style scoped>
.smile-rating-container {
  display: flex;
  justify-content: center;
}

.survey-questions__variants-form-group-container {
  padding-top: 30px;
}

.smile-rating__message-container {
  margin-top: 20px;
  text-align: center;
  position: relative;
}

.survey-questions__comment-form-group {
  margin-top: 30px;
}

.smile-rating__message-container:empty {
  display: none;
}

.smile-rating__message-container .smile-rating__message {
  margin-top: 10px;
}

.smile-rating__message {
  font-size: var(--fqz-poll-font-size, 13px);
  overflow: hidden;
  font-weight: 700;
  line-height: 1.1;
}

.skip-container {
  margin-top: 30px;
}

.gallery-container {
  margin-bottom: 30px;
  margin-top: -10px;
}

.smile-rating-container--simplified.smile-rating-container--has-selected-item:not(.smile-rating-container--has-label) {
  padding-bottom: 9px;
}

.smile-rating-container--simplified.smile-rating-container--has-selected-item.smile-rating-container--like:not(
    .smile-rating-container--has-label
  ) {
  padding-bottom: 14px;
}

@media (max-width: 679px) {
  .smile-rating__message {
    font-size: 14px;
  }

  .smile-rating-container--simplified.smile-rating-container--has-selected-item:not(
      .smile-rating-container--has-label
    ) {
    padding-bottom: 8px;
  }

  .smile-rating-container--simplified.smile-rating-container--has-selected-item.smile-rating-container--like:not(
      .smile-rating-container--has-label
    ) {
    padding-bottom: 14px;
  }
}
</style>
