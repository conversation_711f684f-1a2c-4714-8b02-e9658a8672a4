<script setup>
import { saveAnswer } from '@entities/poll/api/index.js'
import { usePollStore } from '@entities/poll/model/store.js'
import { serialize, withRootUrl } from '@shared/helpers/general.js'
import { usePreviewStore } from '@shared/store/previewStore.js'
import { useTranslationsStore } from '@shared/store/translationsStore'
import { useSimplifiedStore } from '@shared/store/useSimplifiedStore'
import { useTabletStore } from '@shared/store/useTabletStore.ts'
import Check from '@shared/ui/Check.vue'
import Dialog from '@shared/ui/Dialog/Dialog.vue'
import DialogAlertClick from '@shared/ui/DialogAlertClick.vue'
import FormGroup from '@shared/ui/FormGroup.vue'
import PollTimer from '@shared/ui/PollTimer.vue'
import SlideTransition from '@shared/ui/SlideTransition.vue'
import Textarea from '@shared/ui/Textarea.vue'
import Tooltip from '@shared/ui/Tooltip/Tooltip.vue'
import { useMediaQuery } from '@vueuse/core'
import debounce from 'lodash.debounce'
import { DialogClose } from 'radix-vue'
import { computed, nextTick, onBeforeUnmount, onMounted, ref, toValue, watch } from 'vue'

const props = defineProps({
  question: {
    type: Object,
    required: true,
  },
})

const simplifiedStore = useSimplifiedStore()
const translationsStore = useTranslationsStore()
const tabletStore = useTabletStore()
const pollStore = usePollStore()
const previewStore = usePreviewStore()

const params = new URLSearchParams(window.location.search)
const isEditMode = params.get('edit') === '1'

const contentModalRef = ref(null)

const imgRef = ref(null)
const isSmallImage = ref(false)

const imgWrapperRef = ref(null)
const showLeftShadow = ref(false)
const showRightShadow = ref(false)
const showTopShadow = ref(false)
const showBottomShadow = ref(false)

const isOpen = ref(false)
const clickPosition = ref({ x: 0, y: 0 })

const displayedPoints = ref([])

const isMobile = useMediaQuery('(max-width: 679px)')
const renderTooltipComponent = computed(() => {
  if (tabletStore.isTabletMode || isMobile.value) {
    return false
  }
  return true
})

const firstClickTestQuestionClasses = computed(() => ({
  'first-click-test-question': true,
  'first-click-test-question--in-iframe': simplifiedStore.isInIframe,
  'first-click-test-question--simplified': simplifiedStore.isSimplifiedMode,
}))

const questionId = computed(() => props.question.questionId)
const imageDisplayTime = computed(() => props.question.imageDisplayTime.value)
const isSkipped = computed(() => toValue(props.question.skipped))
const isImageVisible = props.question.isImageVisible

const mobileViewMode = computed(() => props.question.mobileView.value)

async function showImage() {
  // Если есть сохраненные точки и таймер уже запущен, просто показываем изображение
  if (props.question.clickPoints.value.length > 0
    && props.question.timerStarted.value) {
    isImageVisible.value = true
    nextTick(() => {
      checkImageSize()
      updateDisplayedPoints()
    })
    return
  }

  if (props.question.skipped.value) {
    props.question.skipped.value = false
    return
  }

  if (previewStore.isPreviewMode) {
    if (!props.question.timerStarted.value) {
      props.question.initTimer() // Инициализация без серверного времени
      props.question.timerStarted.value = true
    }
  }
  else if (!props.question.timerStarted.value && !props.question.requestSent.value) {
    props.question.requestSent.value = true
    try {
      const response = await saveAnswer({
        authKey: pollStore.authKey,
        questionId: questionId.value,
        item: serialize({ time_start: 1 }),
      })
      props.question.initTimer(response.time_start)
      props.question.timerStarted.value = true
    }
    catch (error) {
      console.error('Error saving start time:', error)
      props.question.initTimer()
      props.question.timerStarted.value = true
    }
  }

  isImageVisible.value = true
  nextTick(() => {
    checkImageSize()
    updateDisplayedPoints()
  })
}

// Функция для обновления displayedPoints на основе clickPoints и масштаба
function updateDisplayedPoints() {
  if (!imgRef.value || !imgWrapperRef.value) {
    displayedPoints.value = []
    return
  }

  const wrapperRect = imgWrapperRef.value.getBoundingClientRect()
  const imgRect = imgRef.value.getBoundingClientRect()

  // Рассчитываем смещение изображения внутри wrapper
  const offsetX = imgRect.left - wrapperRect.left
  const offsetY = imgRect.top - wrapperRect.top

  displayedPoints.value = props.question.clickPoints.value.map((point) => {
    // Рассчитываем позиции с учетом масштаба и текущего скролла
    const scaleX = imgRef.value.clientWidth / imgRef.value.naturalWidth
    const scaleY = imgRef.value.clientHeight / imgRef.value.naturalHeight

    return {
      x: offsetX + (point.x * scaleX),
      y: offsetY + (point.y * scaleY),
      originalX: point.x,
      originalY: point.y,
      id: `point-${point.x}-${point.y}-${point.time}`,
    }
  })
}

function handleImageClick(event) {
  if (!imgRef.value)
    return

  if (!isEditMode) {
    if (imageDisplayTime.value && props.question.timeExpired.value) {
      clickPosition.value = { x: event.clientX, y: event.clientY }
      isOpen.value = true
      return
    }
  }

  const rect = imgRef.value.getBoundingClientRect()
  const x = event.clientX - rect.left
  const y = event.clientY - rect.top

  const clickedPoint = findClickedPoint(x, y)

  if (clickedPoint && props.question.allowCancelClick.value) {
    pointCoordinate(x, y)
    return
  }

  if (!props.question.checkMaxClickPoints.value) {
    clickPosition.value = { x: event.clientX, y: event.clientY }
    isOpen.value = true
  }

  pointCoordinate(x, y)
}

function pointCoordinate(x, y) {
  const scaleX = imgRef.value.naturalWidth / imgRef.value.clientWidth
  const scaleY = imgRef.value.naturalHeight / imgRef.value.clientHeight
  const originalX = Math.round(x * scaleX)
  const originalY = Math.round(y * scaleY)

  const pointRemoved = props.question.addClickPoint(originalX, originalY)

  if (pointRemoved !== null) {
    updateDisplayedPoints()
  }
}

function findClickedPoint(x, y) {
  if (!displayedPoints.value.length)
    return null

  // Проверяем, попадает ли клик в область какой-либо точки (например, в радиусе 10px)
  return displayedPoints.value.find((point) => {
    const dx = point.x - x
    const dy = point.y - y
    return Math.sqrt(dx * dx + dy * dy) <= 20
  })
}

function handleImageLoad() {
  checkImageSize()

  nextTick(() => {
    updateDisplayedPoints()
  })
}

const debouncedHandleResize = debounce(() => {
  updateDisplayedPoints()
  updateShadows()
}, 100)

function handleResize() {
  debouncedHandleResize()
}

function btnImgDone() {
  isImageVisible.value = false
  props.question.checkFirstOpen.value = true
}

function checkImageSize() {
  nextTick(() => {
    if (imgRef.value && isMobile.value) {
      const img = imgRef.value
      const imgWidth = img.naturalWidth
      const imgHeight = img.naturalHeight
      const viewportWidth = window.innerWidth
      const viewportHeight = window.innerHeight - 55 // учитываем высоту панели кнопок

      if (mobileViewMode.value === 0) {
        // Для режима по ширине
        const scaledHeight = (imgHeight * viewportWidth) / imgWidth
        isSmallImage.value = scaledHeight < viewportHeight
      }
      else {
        // Для режима по высоте
        const scaledWidth = (imgWidth * viewportHeight) / imgHeight
        isSmallImage.value = scaledWidth < viewportWidth
      }
    }
  })
}

function updateShadows() {
  if (!imgWrapperRef.value)
    return

  const el = imgWrapperRef.value
  const isScrollableX = el.scrollWidth > el.clientWidth
  const isScrollableY = el.scrollHeight > el.clientHeight

  // Горизонтальные тени
  if (isScrollableX) {
    showLeftShadow.value = el.scrollLeft > 0
    showRightShadow.value = el.scrollLeft < el.scrollWidth - el.clientWidth - 1
  }
  else {
    showLeftShadow.value = false
    showRightShadow.value = false
  }

  // Вертикальные тени
  if (isScrollableY) {
    showTopShadow.value = el.scrollTop > 0
    showBottomShadow.value = el.scrollTop < el.scrollHeight - el.clientHeight - 1
  }
  else {
    showTopShadow.value = false
    showBottomShadow.value = false
  }
}

watch(() => [imgRef.value?.clientWidth, imgRef.value?.clientHeight], () => {
  updateShadows()
  updateDisplayedPoints()
}, { flush: 'post' })

watch(() => props.question.clickPoints.value, () => {
  updateDisplayedPoints()
}, { deep: true })

function handleScroll() {
  updateShadows()
  updateDisplayedPoints()
}

onMounted(() => {
  nextTick(() => {
    updateShadows()
    if (imgWrapperRef.value) {
      imgWrapperRef.value.addEventListener('scroll', handleScroll)
      window.addEventListener('resize', handleResize)
    }

    if (imgRef.value && imgRef.value.complete) {
      checkImageSize()
    }
  })

  if (!props.question.timeExpired.value && props.question.timerStarted.value) {
    props.question.startTimer()
  }

  // Если есть сохраненные точки и время старта, инициализируем таймер
  if (props.question.clickPoints.value.length > 0
    && props.question.serverStartTime.value) {
    props.question.initTimer()
  }

  window.addEventListener('keydown', (e) => {
    if (e.key === 'Escape' && isImageVisible.value) {
      e.preventDefault()
      e.stopPropagation()
      isImageVisible.value = false
    }
  })
})

onBeforeUnmount(() => {
  if (imgWrapperRef.value) {
    imgWrapperRef.value.removeEventListener('scroll', handleScroll)
    window.removeEventListener('resize', handleResize)
  }

  window.removeEventListener('keydown', (e) => {
    if (e.key === 'Escape' && isImageVisible.value) {
      e.preventDefault()
      e.stopPropagation()
    }
  })
})

const remainingTime = computed(() => {
  return props.question.remainingTime.value || 0
})

const urlImg = computed(() => {
  if (previewStore.isPreviewMode) {
    return withRootUrl(props.question.image.value.preview)
  }

  return withRootUrl(props.question.image.value.src)
})
</script>

<template>
  <div :class="firstClickTestQuestionClasses">
    <form :id="`answer-form-${question.id}`">
      <div class="survey-questions__wrapper_pre">
        <transition name="fade">
          <div
            v-if="isImageVisible"
            class="first-click-test__fullscreen-image"
            :class="{
              'first-click-test__fullscreen-image--width': isMobile && mobileViewMode === 0,
              'first-click-test__fullscreen-image--height': isMobile && mobileViewMode === 1,
            }"
            @click.stop
          >
            <div class="first-click-test__fullscreen-content">
              <div v-if="question.image.value" class="interscreen-block">
                <div
                  class="interscreen-image"
                  :class="{
                    'interscreen-image--width-mode': isMobile && mobileViewMode === 0,
                    'interscreen-image--height-mode': isMobile && mobileViewMode === 1,
                    'interscreen-image--small-image': isSmallImage,
                  }"
                >
                  <!-- Горизонтальные тени -->
                  <transition name="fade-fast">
                    <div
                      v-if="showLeftShadow"
                      class="scroll-shadow scroll-shadow--left"
                    />
                  </transition>
                  <transition name="fade-fast">
                    <div
                      v-if="showRightShadow"
                      class="scroll-shadow scroll-shadow--right"
                    />
                  </transition>

                  <!-- Вертикальные тени -->
                  <transition name="fade-fast">
                    <div
                      v-if="showTopShadow"
                      class="scroll-shadow scroll-shadow--top"
                    />
                  </transition>
                  <transition name="fade-fast">
                    <div
                      v-if="showBottomShadow"
                      class="scroll-shadow scroll-shadow--bottom"
                    />
                  </transition>
                  <div ref="imgWrapperRef" class="interscreen-image__wrapper" @scroll="handleScroll">
                    <img
                      ref="imgRef"
                      :src="urlImg"
                      :alt="question.image.value.label"
                      @load="() => {
                        handleImageLoad()
                        checkImageSize()
                      }"
                      @click.stop="handleImageClick"
                    >

                    <div
                      v-for="point in displayedPoints"
                      :key="point.id"
                      class="dot-click"
                      :style="{
                        left: `${point.x - 10}px`,
                        top: `${point.y - 10}px`,
                        pointerEvents: 'none',
                      }"
                    />

                    <DialogAlertClick
                      v-model:open="isOpen"
                      variant="plain"
                      :position="clickPosition"
                      :dismiss-after="2000"
                      :text="translationsStore.t('Выбрано максимальное количество точек')"
                      :preferred-position="isMobile ? 'right' : 'top'"
                      :offset="isMobile ? 15 : 10"
                    >
                      <template #content>
                        <span
                          v-if="question.timeExpired.value && imageDisplayTime"
                          class="first-click-test__alert-text"
                        >
                          {{ translationsStore.t('Время ответа истекло') }}
                        </span>
                        <span
                          v-else-if="!question.checkMaxClickPoints.value"
                          class="first-click-test__alert-text"
                        >
                          {{ translationsStore.t('Выбрано максимальное количество точек') }}
                        </span>
                      </template>
                    </DialogAlertClick>
                  </div>
                </div>
              </div>

              <div ref="contentModalRef" class="first-click-test__img-actions-block">
                <div v-if="imageDisplayTime" class="first-click-test__timer-block">
                  <PollTimer
                    :label="translationsStore.t('Оставшееся время')"
                    :elapsed-time="remainingTime"
                  />
                </div>
                <div class="first-click-test__buttons">
                  <Tooltip
                    v-if="renderTooltipComponent"
                    view="hint"
                    align-position="center"
                    :max-width-content="680"
                    not-design
                    :disable-close-on-trigger-click="true"
                  >
                    <template #trigger>
                      <button
                        class="btn__img btn__img--show"
                        @click.stop.prevent
                      >
                        {{ translationsStore.t('Показать вопрос') }}
                      </button>
                    </template>
                    <template #content>
                      <p class="tooltip__title">
                        {{ question.tooltipTitle }}
                      </p>
                      <p v-if="question.tooltipContent.value" class="tooltip__description">
                        {{ question.tooltipContent }}
                      </p>
                    </template>
                  </Tooltip>
                  <Dialog
                    v-else
                    content-class="first-click-test__hint-content"
                    :show-overlay="false"
                    :portal-to="contentModalRef"
                    :content-style="{
                      position: 'absolute',
                      bottom: '100%',
                      top: '0',
                      left: '50%',
                      height: 'min-content',
                      transform: 'translate(-50%, calc(-100% - 15px))',
                    }"
                  >
                    <template #trigger>
                      <button
                        class="btn__img btn__img--show"
                        @click.stop.prevent
                      >
                        {{ translationsStore.t('Показать вопрос') }}
                      </button>
                    </template>
                    <div class="dialog-hint-content__inner">
                      <div class="tooltip__title" v-html="question.tooltipTitle.value" />
                      <p v-if="question.tooltipContent" class="tooltip__description">
                        {{ question.tooltipContent }}
                      </p>
                      <DialogClose as-child>
                        <button class="dialog-hint-content__close" type="button">
                          {{ translationsStore.t('Закрыть') }}
                        </button>
                      </DialogClose>
                    </div>
                  </Dialog>
                  <button
                    class="btn__img btn__img--done"
                    @click.stop.prevent="btnImgDone"
                  >
                    {{ translationsStore.t('Готово') }}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </transition>

        <FormGroup
          :error="question.error.value"
          :error-attrs="{ class: 'first-click-test--error' }"
        >
          <div>
            <p
              v-if="question.checkFirstOpen.value || question.clickPoints.value.length"
              class="first-click-test__add-point"
            >
              <span v-if="question.timeExpired.value && imageDisplayTime">
                <template v-if="question.showExpiredMessage.value">
                  {{ translationsStore.t('Время ответа истекло') }}.
                </template>
              </span>
              <span>
                <span>{{ translationsStore.t('Добавлено точек') }}: </span>
                <span :class="{ 'first-click-test__add-point--null': question.clickPoints.value.length < 1 }">
                  {{ question.clickPoints.value.length }}
                </span>
              </span>
            </p>
            <div class="first-click-test__button-container">
              <button
                class="btn first-click-test__show-img"
                :class="{ 'first-click-test__skipped': isSkipped }"
                type="button"
                @click="showImage"
              >
                {{ question.firstClickButtonText }}
              </button>
            </div>
          </div>
        </FormGroup>

        <SlideTransition>
          <div
            v-if="question.commentController.enabled.value && !question.skipped.value"
            class="survey-questions__comment-form-group"
          >
            <FormGroup
              :label="question.commentController.title.value"
              :error="question.commentController.error.value"
            >
              <Textarea
                v-model="question.commentController.value.value"
                :maxlength="question.commentController.maxLength.value"
                :minlength="question.commentController.minLength.value"
                :placeholder="question.commentController.placeholderText.value"
                :is-invalid="!!question.commentController.error.value"
              />
            </FormGroup>
          </div>
        </SlideTransition>

        <div v-if="question.skip.value" class="skip-container">
          <Check v-model="question.skipped.value" :label="question.skipText.value" />
        </div>
      </div>
    </form>
  </div>
</template>

<style scoped>
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.fade-fast-enter-active,
.fade-fast-leave-active {
  transition: opacity 0.2s ease;
}

.fade-fast-enter-from,
.fade-fast-leave-to {
  opacity: 0;
}

.survey-questions__comment-form-group {
  margin-top: 30px;
}

.btn {
  padding: 14px 25px;
  cursor: pointer;
  background-color: var(--fqz-poll-start-button-background-color);
  color: var(--fqz-poll-start-button-text-color);
  border-radius: var(--fqz-poll-start-button-radius);
  border: 2px solid var(--fqz-poll-start-button-stroke-color);
  font-size: 16px;
  line-height: 1.1;
  font-weight: 700;
  align-self: center;
  font-family: inherit;
  transition: opacity 0.3s;
  text-decoration: none;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn:not(:disabled):hover {
  opacity: 0.8;
}

.first-click-test__button-container {
  text-align: center;
  display: flex;
  width: 100%;
  justify-content: center;
}

.first-click-test__show-img {
  padding-left: 25px;
  padding-right: 25px;
  white-space: nowrap;
  height: 48px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  box-shadow: none !important;
}

.skip-container {
  margin-top: 30px;
  text-align: center;
}

.first-click-test__skipped {
  opacity: 50%;
}

.first-click-test__fullscreen-image {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-index-modal);
  padding: 16px 15px;
}

.first-click-test-question--simplified .first-click-test__fullscreen-image {
  padding: 0 0 15px 0;
}

.first-click-test__fullscreen-content {
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 15px;
  height: 100%;
  width: 100%;
  box-sizing: border-box;
}

.first-click-test__fullscreen-image .interscreen-block {
  width: 100%;
  display: flex;
  justify-content: center;
  margin: 0;
  max-height: calc(100% - 55px);
}

.first-click-test__fullscreen-image .interscreen-image {
  height: 100% !important;
  margin: 0;
}

.first-click-test__fullscreen-image .interscreen-image img {
  height: 100%;
  object-fit: contain;
}

.interscreen-block {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: center;
  width: 100%;
}

.interscreen-image {
  position: relative;
  overflow: hidden;
  display: flex;
  justify-content: center;
}

.interscreen-image__wrapper {
  width: 100%;
  height: 100%;
  overflow: auto;
  -webkit-overflow-scrolling: touch;
  scrollbar-width: none;
}

.interscreen-image__wrapper::-webkit-scrollbar {
  display: none;
}

.scroll-shadow {
  position: absolute;
  pointer-events: none;
  z-index: 10;
}

.scroll-shadow--left {
  left: 0;
  top: 0;
  bottom: 0;
  width: 15px;
  background: linear-gradient(90deg, #0b0b0b 0%, rgba(0, 0, 0, 0) 100%);
}

.scroll-shadow--right {
  right: 0;
  top: 0;
  bottom: 0;
  width: 15px;
  background: linear-gradient(270deg, #0b0b0b 0%, rgba(0, 0, 0, 0) 100%);
}

.scroll-shadow--top {
  top: 0;
  left: 0;
  right: 0;
  height: 15px;
  background: linear-gradient(0, rgba(0, 0, 0, 0) 0%, #0b0b0b 100%);
}

.scroll-shadow--bottom {
  bottom: 0;
  left: 0;
  right: 0;
  height: 15px;
  background: linear-gradient(0, #0b0b0b 0%, rgba(0, 0, 0, 0) 100%);
}

.interscreen-image .interscreen-image__wrapper img {
  max-height: 100%;
  max-width: 100%;
  width: auto;
  height: auto;
  cursor: pointer;
}

.first-click-test__img-actions-block {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-wrap: wrap;
  gap: 20px;
  height: 40px;
}

.first-click-test__timer-block {
  display: flex;
  flex-direction: column;
  color: #ffffff;
}

.first-click-test__buttons {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 20px;
}

.btn__img {
  cursor: pointer;
  padding: 11px 20px;
  height: 36px;
  font-size: 14px;
  line-height: 100%;
  font-weight: 400;
  border-radius: 4px;
  border: 0;
}

.btn__img--show {
  background: transparent;
  color: #ffffff;
  border: 1px solid #ffffff;
}

.btn__img--done {
  background: #ffffff;
}

.tooltip__title {
  font-size: 19px;
  line-height: 110%;
  font-weight: 700;
  color: #000000;
  text-align: center;
}

.tooltip__description {
  margin-top: 15px;
  font-size: 14px;
  line-height: 110%;
  font-weight: 400;
  color: #000000;
  text-align: center;
}

.dialog-hint-content__close {
  width: 100%;
  background: none;
  border: 0;
  padding: 0;
  margin-top: 15px;
  font-size: 15px;
  line-height: 110%;
  font-weight: 600;
  color: #000000;
  text-align: center;
  cursor: pointer;
}

.dot-click {
  position: absolute;
  background: #ff0000;
  border: 3px solid #ffffff;
  width: 20px;
  height: 20px;
  border-radius: 100px;
  box-shadow: 0 5px 15px 0 rgba(46, 47, 49, 0.3);
  pointer-events: none;
  z-index: 2;
  will-change: transform;
}

.first-click-test__add-point {
  text-align: center;
  margin-bottom: 30px;
  color: var(--fqz-poll-text-on-place);
  font-size: var(--fqz-poll-font-size);
  font-weight: 400;
}

.first-click-test__add-point--null {
  opacity: 50%;
}

.first-click-test__alert-text {
  text-align: center;
  font-weight: 400;
  font-size: 14px;
  line-height: 130%;
  color: #000000;
}

.first-click-test__hint-content {
  background: #ffffff !important;
}

:deep(.form-group__error) {
  margin-top: 30px;
}

@media screen and (max-width: 679px) {
  .btn {
    font-weight: 500;
    padding: 8px 25px;
    height: 36px;
  }

  .first-click-test__img-actions-block {
    flex-direction: column;
    height: auto;
    gap: 15px;
  }

  .first-click-test__timer-block {
    display: block;
    height: auto;
  }

  .first-click-test__buttons {
    position: relative;
    gap: 15px;
  }

  .btn__img {
    height: 30px;
    padding: 9px 15px;
    font-size: 12px;
  }

  .first-click-test__fullscreen-image {
    padding: 0;
  }

  .first-click-test__fullscreen-image--width {
    padding: 15px 0;
  }

  .first-click-test__fullscreen-image--height {
    padding: 15px;
  }

  .first-click-test__fullscreen-image .interscreen-block {
    width: 100%;
    overflow: hidden;
    display: flex;

    justify-content: center;
    align-items: flex-start;
    -webkit-overflow-scrolling: touch;
  }

  .interscreen-image {
    overflow: auto;
    justify-content: center;
  }

  /* mobile_view = 0 */
  .first-click-test__fullscreen-image--width .interscreen-block {
    overflow-x: auto;
    overflow-y: hidden;
    scroll-snap-type: x mandatory;
  }

  .first-click-test__fullscreen-image--width .interscreen-image--width-mode {
    height: 100%;
    width: auto;
    max-width: none;
  }

  .first-click-test__fullscreen-image--width .interscreen-image__wrapper {
    padding: 0 15px;
  }

  .first-click-test__fullscreen-image--width .interscreen-image--width-mode img {
    height: 100%;
    width: auto;
    max-width: none;
    object-fit: contain;
  }

  /* mobile_view = 1 */
  .first-click-test__fullscreen-image--height .interscreen-block {
    overflow-y: auto;
    overflow-x: hidden;
    scroll-snap-type: y mandatory;
  }

  .first-click-test__fullscreen-image--height .interscreen-image--height-mode {
    width: 100%;
    height: auto;
    max-height: none;
  }

  .first-click-test__fullscreen-image--height .interscreen-image--height-mode img {
    width: 100%;
    height: auto;
    max-height: none;
    object-fit: contain;
  }

  .interscreen-image--small-image {
    justify-content: flex-start;
  }

  .first-click-test__add-point {
    font-size: 14px;
    color: #000000;
    line-height: 110%;
  }

  :deep(.poll-timer) {
    flex-direction: row;
  }

  :deep(.poll-timer__time) {
    font-size: 12px;
  }

  .first-click-test-question--in-iframe .first-click-test__show-img {
    height: 36px;
    padding: 9px 0;
    font-size: 15px;
    width: 100%;
  }

  .first-click-test-question--in-iframe .survey-questions__comment-form-group,
  .first-click-test-question--in-iframe .skip-container {
    margin-top: 20px;
  }

  .first-click-test-question--in-iframe .first-click-test__add-point {
    display: none;
  }

  .first-click-test-question--in-iframe .interscreen-block {
    justify-content: center;
  }
}
</style>
