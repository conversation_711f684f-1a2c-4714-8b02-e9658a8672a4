import { usePointsStore } from '@features/points/store/pointsStore'
import useVariablesReplacer from '@shared/composables/useVariablesReplacer'
import { DONOR_SELECT_TYPE } from '@shared/constants'
import {
  BEHAVIOR_ALWAYS,
  JUMP_TO_DEFAULT_END_SCREEN,
  JUMP_TO_END_SCREEN,
  JUMP_TO_QUESTION,
  VIEW_LOGIC,
} from '@shared/constants/logic'
import { prepareBaseQuestionPreviewData } from '@shared/helpers/previewDataPreparator'
import { declOfNum } from '@shared/helpers/string'
import { usePreviewStore } from '@shared/store/previewStore'
import { useTranslationsStore } from '@shared/store/translationsStore'
import ee from 'event-emitter'
import { computed, isRef, reactive, ref, shallowRef, toValue, watch } from 'vue'
import { CLASSIFIER_QUESTION, VARIANTS_QUESTION } from './types'

export class BaseQuestion {
  /**
   * @param {object} data - Данные вопроса
   * @param {string | null} mode - Тип режима превью (null, 'preview-default', 'preview-design', 'preview-full')
   */
  constructor(data, mode = null) {
    ee(this)
    this.mode = mode
    this.data = data

    /**
     * @type {boolean}
     * @description Флаг, указывающий на то, что превью не функциональное
     * В данном режиме не работает логика отображения, донор-реципиент, логика перехода и т.д.
     * А также в данном режиме не работает функционал переменных
     */
    this.isNonFunctionalPreview = mode === 'preview-default'

    /**
     * @type {boolean}
     * @description Флаг, указывающий на то, что превью является дизайн-превью
     */
    this.isDesignPreview = mode === 'preview-design'

    /**
     * @type {boolean}
     * @description Флаг, указывающий на то, что превью открыто в новой вкладке
     */
    this.isFullPreview = mode === 'preview-full'

    /**
     * @type {boolean}
     * @description Флаг, указывающий на то, что превью функциональное
     */
    this.isFunctionalPreview = this.isDesignPreview || this.isFullPreview

    /**
     * @type {boolean}
     * @description Флаг, указывающий на то, что вопрос отображается в режиме превью
     */
    this.isPreviewMode = this.isFunctionalPreview || this.isNonFunctionalPreview

    /**
     * @type {boolean}
     * @description Флаг, указывающий на то, что вопрос имеет баллы
     */
    this.hasPoints = ref(!!data.hasPoints && !this.isPreviewMode)

    const translationsStore = useTranslationsStore()
    this.translations = computed(() => {
      const translations = toValue(translationsStore.getQuestionTranslation(data)) || {}
      return translations
    })
    const previewStore = usePreviewStore()

    /**
     * @type {object | null}
     * @description Предыдущий ответ на вопрос от респондента
     */
    this.previousAnswer = data.answer

    /**
     * @type {boolean}
     * @description Флаг, указывающий на наличие предыдущего ответа на вопрос от респондента
     */
    this.hasPreviousAnswer = this.previousAnswer !== null && this.previousAnswer !== undefined

    const empty = this.previousAnswer?.isEmpty
    this.isEmpty = empty !== undefined ? empty : false

    // Содержит все переменные из опроса (поле "variables")
    this.rawVariables = {}

    // Содержит переменные, которые используются в данном вопросе
    this.variables = reactive({})
    this._subscriptions = []

    this.touched = ref(false)
    this.touchedSubscriptions = []
    this.blocked = ref(false)

    this.value = data.value
    this.questionId = data.question_id

    this.isCustomFieldChecked = data.isCustomFieldChecked
    this.textFieldValue = data.textFieldValue || ''
    this.dateValue = data.dateValue
    this.radioButtonCheckedValue = data.radioButtonCheckedValue
    this.timeValue = data.timeValue
    this.files = data.files
    this.stars = data.stars
    this.comment = data.comment
    this.donorId = data.donor || null

    /**
     * @type {boolean}
     * @description Проверка на наличие вопроса-донора
     * @NOTE: В режиме нефункционального превью, считаем, что вопрос не имеет донора
     */
    this.hasDonor = !!this.donorId && !this.isNonFunctionalPreview

    /**
     * @type {BaseQuestion}
     * @description Ворос-донор. Используется для функционала "Донор-Реципиент"
     * По умолчанию null.
     * Если вопрос является реципиентом, то в этом свойстве будет храниться ссылка на вопрос-донор
     */
    this.donorQuestion = shallowRef(null)

    /**
     * @type {Array<number>}
     * @description Массив идентификаторов, содержащий id вариантов донора (для строк)
     */
    this.donorRows = data.donor_rows
    this.hasDonorRows = !!this.donorRows && !this.isNonFunctionalPreview

    /**
     * @type {Array<number>}
     * @description Массив идентификаторов, содержащий id вариантов донора (для столбцов)
     */
    this.donorColumns = data.donor_columns
    this.hasDonorColumns = !!this.donorColumns && !this.isNonFunctionalPreview

    /**
     * @type {boolean}
     * @description Брать выбранные варианты из донора или невыбранные
     */
    this.donorSelected = data.donorSelected
    this.donorColumnsSelected = data.donorColumnsSelected

    this.textFieldParam = data.textFieldParam || {}
    this.placeholderText = data.placeholderText || ''

    this.mediaVariantsType = data.variantsType
    this.mediaVariantsMediaType = data.chooseType
    this.mediaVariants = data.chooseMedia
    this.choosedMediaVariants = []

    this.showQuestion = data.showQuestion

    this.isValid = true
    this.domNodeElement = ref(null)

    this.type = data.type

    this.langs = data.langs

    this.id = data.id
    this.alias = data.alias

    // Make name writable with a ref
    this._name = ref(data.name || '')
    this.name = computed({
      get: () => this.translations.value.name || this._name.value || '',
      set: (value) => {
        this._name.value = value
      },
    })

    // Make description writable
    this._description = ref(data.description || '')
    this.description = computed({
      get: () => this._description.value,
      set: (value) => {
        this._description.value = value
      },
    })

    // Make description_html writable
    this._description_html = ref(data.description_html || '')
    this.description_html = computed({
      get: () => {
        const translations = this.translations.value
        const description = translations?.description_html || this._description_html.value || ''
        return this.replaceVariables(description)
      },
      set: (value) => {
        this._description_html.value = value
      },
    })

    // Make subdescription writable
    this._subdescription = ref(data.subdescription || '')
    this.subdescription = computed({
      get: () => this.replaceVariables(this.translations.value?.sub_description || this._subdescription.value || ''),
      set: (value) => {
        this._subdescription.value = value
      },
    })

    this.isRequired = ref(!!data.isRequired)

    this.logic = data.questionLogic
    this.viewLogic = data.questionViewLogic

    /**
     * Содержит вопросы-зависимости по полю question_id
     * Используется для функционала: Донор-Реципиент
     * Используется для логики отображения вопросов
     * @type {Record<string, BaseQuestion>}
     */
    this.dependencyQuestionsById = shallowRef({})

    this._showNumber = ref(true)
    this.showNumber = computed({
      get: () => this._showNumber.value,
      set: value => this._showNumber.value = value,
    })
    this.selectedDonorVariants = computed(() => {
      const donorVariants = this.getDonorVariants()
      return donorVariants
    })

    /**
     * @type {import('vue').Ref<boolean>}
     * Реактивная переменная, которая отслеживает,
     * взаимодействовал ли пользователь с вопросом
     */
    this.hasInteracted = ref(false)

    // Отслеживаем изменение данных в режиме "превью"
    watch(() => previewStore.previewData, (newPreviewData) => {
      if (newPreviewData && newPreviewData.questionId === this.questionId) {
        this.updateFromPreview(newPreviewData)
      }
    })
  }

  /**
   * Возвращает массив выбранных/невыбранных вариантов донора
   * Донором может быть вопрос "Варианты ответа", либо "Классификатор"
   * Если donorSelected === DONOR_SELECT_TYPE.SELECTED, возвращает выбранные варианты
   * Если donorSelected === DONOR_SELECT_TYPE.UNSELECTED, возвращает невыбранные варианты
   * @param {BaseQuestion|null} donor Вопрос-донор. Обычно мы берем его из свойства this.donorQuestion
   * @param {string | null} donorSelected Брать выбранные варианты из донора или невыбранные
   * @returns {Array<object>} Массив вариантов
   */
  getDonorVariants(donor = null, donorSelected = null) {
    const donorQuestion = toValue(donor || this.donorQuestion)
    if (!donorQuestion)
      return []

    const donorSelectedType = donorSelected !== null ? donorSelected : this.donorSelected
    if (donorQuestion?.type === VARIANTS_QUESTION) {
      // Донор - Ворос "Варианты ответов"
      const variantsController = donorQuestion.variantsController
      const selectedAndUnselectedVariants = toValue(variantsController.selectedAndUnselectedVariants)

      if (donorSelectedType === DONOR_SELECT_TYPE.SELECTED) {
        return selectedAndUnselectedVariants.selected || []
      }

      // Если в списке есть "Свой вариант", то он не должен учитываться при выборе невыбранных вариантов
      const unselectedWithoutSelfVariant = selectedAndUnselectedVariants.unselected
        .filter(variant => variant?.id !== 'is_self_answer')
      return unselectedWithoutSelfVariant || []
    }
    else {
      // Донор - Классификатор
      const selectedAndUnselectedNodes = toValue(donorQuestion.selectedAndUnselectedNodes)

      if (donorSelectedType === DONOR_SELECT_TYPE.SELECTED) {
        return selectedAndUnselectedNodes.selected || []
      }
      return selectedAndUnselectedNodes.unselected || []
    }
  }

  get isEndScreen() {
    return false
  }

  get isStartScreen() {
    return false
  }

  updateValidity() {
    setTimeout(() => {
      this.isValid = this.checkValidity()
    }, 0)
  }

  checkValidity() {
    return true
  }

  getSymbolWordForm(n) {
    return declOfNum(n, ['символ', 'символа', 'символов'])
  }

  /**
   * Возвращает id следующего элемента на основе условий логики вопроса
   * @typedef {object} NextItem
   * @property {string} type - тип следующего элемента
   * @property {string} id - id следующего элемента
   * @returns {NextItem | null} Объект, содержащий тип и id следующего элемента, или null, если нет совпадений по логике
   */
  getNextLogicItemId() {
    if (!this.logic || !this.logic.length)
      return null

    // Сортируем условия логики по порядковому номеру
    const sortedLogic = [...this.logic].sort((a, b) => a.position - b.position)

    // Находим первое совпадение по условию
    const matchingCondition = sortedLogic.find(condition =>
      this.checkCondition(condition),
    )

    if (!matchingCondition)
      return null

    const handleJumpToQuestion = (condition) => {
      const isPageType = !!condition.jump_display_page_id
      return {
        type: isPageType ? 'page' : 'question',
        id: isPageType ? condition.jump_display_page_id : condition.jump_question_id,
      }
    }

    // Определяем тип возвращаемого элемента на основе типа перехода
    switch (matchingCondition.jump) {
      case JUMP_TO_QUESTION:
        return handleJumpToQuestion(matchingCondition)
      case JUMP_TO_DEFAULT_END_SCREEN:
        return {
          type: 'end',
          id: 'end-default',
        }
      case JUMP_TO_END_SCREEN:
        return {
          type: 'end',
          id: matchingCondition.jump_question_id,
        }
      default:
        return null
    }
  }

  checkCondition(condition) {
    if (this.logic.length === 0)
      return true

    if (condition.behavior === BEHAVIOR_ALWAYS)
      return true
    return false
  }

  /**
   * Добавляет необходимые вопросы в качестве зависимостей
   * @param {Record<string, BaseQuestion>} allQuestionsById - все имеющиеся вопросы в опросе
   */
  addDependencies(allQuestionsById) {
    const hasDonor = !!this.donorId
    const hasViewLogic = Array.isArray(this.viewLogic) && this.viewLogic.length

    if (!hasDonor && !hasViewLogic)
      return

    // Если Включена опция "Донор-Реципиент" и вопрос является реципиентом,
    // то в этом свойстве будет храниться ссылка на вопрос-донор
    if (this.hasDonor) {
      this.donorQuestion.value = allQuestionsById[this.donorId]
    }

    if (Array.isArray(this.viewLogic) && this.viewLogic.length) {
      this.viewLogic.forEach((rule) => {
        const dependentQuestion = allQuestionsById[rule.condition_question_id]
        if (dependentQuestion) {
          this.dependencyQuestionsById.value = {
            ...this.dependencyQuestionsById.value,
            [rule.condition_question_id]: dependentQuestion,
          }
        }
      })
    }
  }

  /**
   * Проверяет, должен ли отображаться вопрос при включенной опции "Логика отображения вопросов"
   * @returns {boolean} true, если вопрос должен быть видим согласно правилам логики отображения, false в противном случае
   */
  isVisibleByViewLogic() {
    if (!this.viewLogic?.length)
      return true // No rules, default to visible

    let shouldShowBasedOnShowIf = false
    let hasShowIfRule = false

    // First pass: Check for any HIDE_IF condition met
    for (const rule of this.viewLogic) {
      if (rule.visibility === VIEW_LOGIC.DISPLAY_TYPE.HIDE_IF) {
        const dependentQuestion = this.dependencyQuestionsById.value[rule.condition_question_id]
        let conditionMet = false

        // Check if the dependent question itself is hidden by its own logic
        if (dependentQuestion && !dependentQuestion.isVisibleByViewLogic?.()) {
          conditionMet = false // If dependency is hidden, condition is not met for HIDE_IF
        }
        else {
          conditionMet = this.checkViewLogicCondition(rule, dependentQuestion || null)
        }

        if (conditionMet) {
          return false // Hide immediately if any HIDE_IF condition is met
        }
      }
      else if (rule.visibility === VIEW_LOGIC.DISPLAY_TYPE.SHOW_IF) {
        hasShowIfRule = true // Mark that at least one SHOW_IF rule exists
      }
    }

    // Second pass (only if no HIDE_IF condition was met): Check SHOW_IF conditions
    if (hasShowIfRule) {
      for (const rule of this.viewLogic) {
        if (rule.visibility === VIEW_LOGIC.DISPLAY_TYPE.SHOW_IF) {
          const dependentQuestion = this.dependencyQuestionsById.value[rule.condition_question_id]
          let conditionMet = false

          // Check if the dependent question itself is hidden by its own logic
          if (dependentQuestion && !dependentQuestion.isVisibleByViewLogic?.()) {
            conditionMet = false // If dependency is hidden, condition is not met for SHOW_IF
          }
          else {
            conditionMet = this.checkViewLogicCondition(rule, dependentQuestion || null)
          }

          if (conditionMet) {
            shouldShowBasedOnShowIf = true // Mark to show if at least one SHOW_IF condition is met
            break // No need to check further SHOW_IF rules
          }
        }
      }
      return shouldShowBasedOnShowIf // Return true if a SHOW_IF was met, false otherwise
    }

    // If we reach here, it means no HIDE_IF conditions were met, and there were no SHOW_IF rules.
    // So, the question should be visible by default in this case.
    return true
  }

  /**
   * Проверяет, должен ли отображаться вопрос при включенной опции "Донор-Реципиент"
   * Проверяем, есть ли выбранные/невыбранные варианты у донора
   * @returns {boolean} Возвращает true, если есть выбранные/невыбранные варианты у донора
   */
  isRecipientVisible() {
    // First check donor visibility
    if (this.hasDonor) {
      const selectedDonorVariants = toValue(this.selectedDonorVariants)
      const donorQuestion = toValue(this.donorQuestion)
      const isDonorRequired = toValue(donorQuestion?.isRequired)
      const takeOnlySelectedVariants = this.donorSelected === DONOR_SELECT_TYPE.SELECTED

      if (donorQuestion?.skipped?.value) {
        return false
      }

      /**
       * @NOTE
       * Если донор является обязательным и мы берем только выбранные варианты,
       * то вопрос-реципиент всегда отображается.
       * Это нужно для того, чтобы у пользователя не отображалась сразу кнопка "Завершить"
       * @see https://redmine.doxswf.ru/issues/4827
       */
      if (isDonorRequired && takeOnlySelectedVariants) {
        return true
      }

      if (!selectedDonorVariants?.length) {
        return false
      }
    }

    // Then check view logic visibility
    return this.isVisibleByViewLogic()
  }

  /**
   * Проверяет, выполняется ли условие логики отображения вопроса
   * @param {object} rule - Условие логики отображения вопроса
   * @param {BaseQuestion | null} dependentQuestion - Вопрос, от которого зависит данное условие.
   * Может быть null, если условие не зависит от другого вопроса (например, параметрическое)
   * @returns {boolean} true, если условие выполняется, false в противном случае
   */
  checkViewLogicCondition(rule, dependentQuestion) {
    // Параметрическое условие (GET параметр)
    if (rule.condition_type === VIEW_LOGIC.CONDITION_TYPE.PARAMETER) {
      const vars = toValue(this.variables)

      const parameter = rule.parameter
      const parameterWithURLPrefix = `URL.${parameter}`
      const value = vars.all?.[parameter] || vars.all?.[parameterWithURLPrefix]

      const conditionValue = String(rule.parameter_value)

      const valueIsEmpty = value === undefined || value === null || value === ''
      const conditionValueIsEmpty = conditionValue === undefined || conditionValue === null || conditionValue === ''

      if (valueIsEmpty || conditionValueIsEmpty) {
        return false
      }

      const handleLessAndGreaterThan = (comparison) => {
        if (comparison === '>') {
          return String(value)?.length > String(conditionValue)?.length
        }
        else {
          return String(value)?.length < String(conditionValue)?.length
        }
      }

      switch (rule.parameter_condition) {
        case VIEW_LOGIC.PARAMETER_CONDITION_TYPE.EQUAL:
          return value === conditionValue
        case VIEW_LOGIC.PARAMETER_CONDITION_TYPE.NOT_EQUAL:
          return value !== conditionValue
        case VIEW_LOGIC.PARAMETER_CONDITION_TYPE.GREATER_THAN:
          return handleLessAndGreaterThan('>')
        case VIEW_LOGIC.PARAMETER_CONDITION_TYPE.LESS_THAN:
          return handleLessAndGreaterThan('<')
        case VIEW_LOGIC.PARAMETER_CONDITION_TYPE.CONTAINS:
          // string contains
          return String(value).includes(String(conditionValue))
        case VIEW_LOGIC.PARAMETER_CONDITION_TYPE.STARTS_WITH:
          return value?.startsWith(conditionValue)
        case VIEW_LOGIC.PARAMETER_CONDITION_TYPE.ENDS_WITH:
          return value?.endsWith(conditionValue)
        default:
          return false
      }
    }

    // Условие по ответу на вопрос
    const isAnswerCondition = rule.condition_type === VIEW_LOGIC.CONDITION_TYPE.ANSWER
    if (isAnswerCondition && dependentQuestion) {
      return dependentQuestion?.resolveViewLogic?.(rule) ?? false
    }

    return false
  }

  /**
   * Добавляет переменные из опроса в вопрос
   * @param {import('vue').Ref<Map<string, string>>} vars Объект с переменными
   * @param {Map<string, import('vue').Ref<string>>} stringifiedAnswers Объект с ответами в виде реактивных строк
   * @returns {void} Добавляет переменные и ответы в вопрос
   */
  addVariables(vars = ref({}), stringifiedAnswers = {}) {
    this.rawVariables = computed(() => toValue(vars))
    this.rawVariablesFilials = computed(() => Object.entries(this.rawVariables.value)
      .filter(([key]) => key.startsWith('FILIAL.'))
      .reduce((acc, [key, value]) => ({ ...acc, [key]: value }), {}))
    this.rawVariablesUrls = computed(() => Object.entries(this.rawVariables.value)
      .filter(([key]) => key.startsWith('URL.'))
      .reduce((acc, [key, value]) => ({ ...acc, [key]: value }), {}))

    this.stringifiedAnswers = toValue(stringifiedAnswers)

    const pointsStore = usePointsStore()

    // Make variables reactive using computed properties
    this.variables.fio = computed(() => this.rawVariables.value?.fio || '')
    this.variables.promocode = computed(() => this.rawVariables.value?.codes?.[this.questionId] || '')
    this.variables.points = computed(() => ({
      enabled: pointsStore.isEnabled,
      isLoading: pointsStore.isLoading,
      totalPoints: pointsStore.totalPoints,
      maxPoints: pointsStore.maxPoints,
      pointsPercentage: pointsStore.pointsPercentage,
      currentPointsInterpretation: pointsStore.currentPointsInterpretation,
    }))
    this.variables.filials = computed(() => this.rawVariablesFilials.value)
    this.variables.urls = computed(() => this.rawVariablesUrls.value)
    this.variables.all = computed(() => this.rawVariables.value)
  }

  replaceVariables(text) {
    const variablesReplacer = useVariablesReplacer()
    return variablesReplacer.replace({
      text,
      variables: this.variables,
      answers: this.stringifiedAnswers,
      previewMode: this.mode,
    })
  }

  getStringifiedAnswer() {
    // This method should be implemented in each question type subclass
    return ''
  }

  /**
   * Отмечает вопрос как взаимодействованный с пользователем
   * У каждого вопроса свои условия, когда он считается взаимодействованным
   * Например, для звездного рейтинга это нажатие на звезду
   * А для текстового поля это ввод любого текста
   * ВАЖНО: Вызов данного метода не означает, что вопрос был полностью валиден
   * Например, для ЗР метод вызывается при нажатии на звезду, но
   * У вопроса может быть обязательный комментарий, или уточняющий вопрос,
   * которые могут быть не заполнены
   * @returns {void}
   */
  markInteracted() {
    this.hasInteracted.value = true
  }

  /**
   * Обновляет вопрос с данными из режима превью
   * @param {object} previewData данные из режима превью
   * @returns {void}
   */
  updateFromPreview(previewData) {
    const preparedData = this.preparePreviewData(previewData)

    // Update basic fields
    this._name.value = preparedData.name
    this._description.value = preparedData.description
    this._description_html.value = preparedData.description_html
    this._subdescription.value = preparedData.subdescription

    const isRequiredChanged = preparedData.isRequired !== this.isRequired.value

    // Update other fields if present
    if (preparedData.isRequired !== undefined) {
      this.isRequired.value = preparedData.isRequired
    }
    else {
      this.isRequired.value = true
    }
    if (preparedData.showNumber !== undefined) {
      this.showNumber.value = preparedData.showNumber
    }

    const clarifyingQuestionEnabled = preparedData.clarifyingQuestionEnabled
    if (clarifyingQuestionEnabled !== undefined && isRef(this.isClarifyingQuestionEnabled)) {
      this.isClarifyingQuestionEnabled.value = clarifyingQuestionEnabled
    }

    if (isRequiredChanged) {
      this.resetFields?.()
    }
  }

  preparePreviewData(previewData) {
    return prepareBaseQuestionPreviewData(previewData)
  }

  /**
   * Возвращает отображаемое имя для варианта реципиента
   * Если донор является древовидным классификатором, использует полный путь, если он доступен
   * @param {object} v - Объект варианта
   * @returns {string} Отображаемое имя варианта
   */
  getRecipientVariantName(v) {
    const donor = toValue(this.donorQuestion)
    const defaultName = v.variant || v.question || ''

    if (!donor)
      return defaultName

    // Проверяем, является ли донор классификатором и древовидным типом
    if (donor?.type === CLASSIFIER_QUESTION && toValue(donor.isTreeType)) {
      return v.path || defaultName
    }

    return defaultName
  }

  /**
   * Проверяет, является ли вариант своим вариантом-реципиентом
   * @param {object} variant - Вариант
   * @returns {boolean} - true, если вариант является своим вариантом-реципиентом, иначе false
   */
  isVariantRecipientSelfAnswer(variant) {
    if (!variant)
      return false

    return (variant.dictionary_element_id === null && variant.question_detail_id === null)
      || variant.dictionary_element_id === '-1'
      || variant.question_detail_id === '-1'
  }

  donorHasLogicLeadingToEnd() {
    const donor = toValue(this.donorQuestion)
    if (!donor)
      return false

    return donor.getNextLogicItemId?.()?.type === 'end'
  }
}
