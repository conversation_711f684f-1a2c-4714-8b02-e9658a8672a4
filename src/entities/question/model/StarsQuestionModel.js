import {
  BEHAVI<PERSON>_ALWAYS,
  BEHAVIOR_MISS,
  B<PERSON><PERSON>VIOR_SELECT,
  BEHAVIOR_UNSELECT,
} from '@shared/constants/logic'
import { prepareStarsQuestionPreviewData } from '@shared/helpers/previewDataPreparator'
import { declOfNum } from '@shared/helpers/string'
import { useTranslationsStore } from '@shared/store/translationsStore'
import { useTabletStore } from '@shared/store/useTabletStore'
import { computed, ref, toValue, watch } from 'vue'
import { CommentController } from '../controllers/CommentController'
import { GalleryController } from '../controllers/GalleryController'
import { VariantsController } from '../controllers/VariantsController'
import { BaseQuestion } from './BaseQuestion'

export class StarsQuestionModel extends BaseQuestion {
  constructor(data, mode) {
    super(data, mode)

    const tabletStore = useTabletStore()

    const { t } = useTranslationsStore()
    this.t = t
    this.data = data
    this.description = computed(() => {
      return this.translations.value.description || data.description
    })

    this.skip = ref(data.skip)
    this._skipText = ref(data.skip_text || '')
    this.skipped = ref(this.previousAnswer?.skipped === 1)

    this.skipText = computed({
      get: () => {
        return this.translations.value.skip_text || this._skipText.value || toValue(this.t('Не готов(а) оценить'))
      },
      set: (value) => {
        this._skipText.value = value
      },
    })

    this.starsConfig = ref(data.starRatingOptions || {})

    this.color = computed(() => this.starsConfig.value.color || 'rgb(248, 205, 28)')
    this.count = computed(() => this.starsConfig.value.count || 5)

    this.enableGallery = ref(data.enableGallery || false)

    this.galleryController = new GalleryController({
      answer: data.answer,
      enabled: toValue(this.enableGallery),
      randomVariantsOrder: false,
      gallery: data.gallery || [],
      selectable: false,
      multiple: false,
      type: 'default',
      isRequired: false,
      skipped: this.skipped,
      translations: this.translations,
    })

    this.domNodeElement = ref(null)
    this.labels = computed(() => {
      let labelsFromTranslation = []
      try {
        const isTranslationLabelsString = typeof this.translations.value.labels === 'string'
        labelsFromTranslation = isTranslationLabelsString ? JSON.parse(this.translations.value.labels) : this.translations.value.labels || []
      }
      catch {
        labelsFromTranslation = []
      }

      const labelsFromData = this.starsConfig.value.labelsArray || []
      const allEmpty = labelsFromTranslation.every(label => !label)
      if (allEmpty) {
        return labelsFromData
      }

      const merged = []

      // Check if labelsFromTranslation is empty
      if (labelsFromTranslation.length === 0) {
        return labelsFromData
      }

      // merge labelsFromTranslation and labelsFromData. If item in labelsFromTranslation is empty, use item from labelsFromData
      for (let i = 0; i < this.count.value; i++) {
        merged.push(labelsFromTranslation[i] || labelsFromData[i])
      }

      return merged
    })
    this.size = computed(() => this.starsConfig.value.size || 'md')
    this.extraQuestionRateFrom = this.starsConfig.value.extra_question_rate_from || -Infinity
    this.extraQuestionRateTo = this.starsConfig.value.extra_question_rate_to || +Infinity

    this.showLabels = ref(data.showLabels || data.show_labels)
    this.showNumbers = ref(data.showNumbers || data.show_numbers)
    this.err = ref(null)

    if (this.count.value > 5)
      this.showLabels.value = false

    // рейтинг
    this.stars = ref(Number.parseInt(this.previousAnswer?.rating) || 0)

    this.starsValidation = computed(() => {
      if (this.skip && this.skipped.value)
        return true
      return this.isRequired.value ? this.stars.value > 0 : true
    })

    this.touched = ref(false)

    watch(this.stars, (newValue) => {
      if (newValue > 0) {
        this.skipped.value = false
        this.markInteracted()
      }
      this.touched.value = true
      this.validateRating()
    })

    // параметры текстового поля (для своего варианта/комментарий)
    this.textFieldParam = data.textFieldParam || {}
    this.placeholderText = data.placeholderText

    // параметры уточняющего вопроса (для assessmentType = 0, answerType = 0)

    // Текст уточняющего вопроса (Например: "Как вы оцениваете качество обслуживания?")
    this.answerText = ref(data.answerText || '')

    this.forAllRates = ref(data.forAllRates || null)
    this.extraQuestionRateFrom = ref(data.starRatingOptions?.extra_question_rate_from || -Infinity)
    this.extraQuestionRateTo = ref(data.starRatingOptions?.extra_question_rate_to || Infinity)
    this.extraRequired = ref(data.extra_required || false)

    this.isCommentRequired = data.comment_required

    // Флаг, который отвечает за то, включен ли уточняющий вопрос
    // @NOTE: Используется исключительно для предпросмотра
    // В обычном режиме мы проверяем есть ли заголовок УВ (answerText)
    this.isClarifyingQuestionEnabled = ref(true)

    this.isClarifyingQuestionVisible = computed(() => {
      if (!this.answerText.value || !this.isClarifyingQuestionEnabled.value)
        return false
      if (this.skip.value && this.skipped.value)
        return false
      const stars = this.stars.value
      if (!stars)
        return false
      if (this.forAllRates.value || (stars >= this.extraQuestionRateFrom.value && stars <= this.extraQuestionRateTo.value))
        return true
      return false
    })

    // текст комментария
    this.comment = ref(data.comment || '')

    this.hasComment = ref(data.comment_enabled === 1)
    this.commentController = new CommentController({
      enabled: computed(() => this.hasComment.value && !this.answerText.value),
      required: this.isCommentRequired,
      skipped: this.skipped,
      value: this.previousAnswer?.answer || '',
      placeholderText: this.placeholderText,
      minLength: this.textFieldParam.min || 0,
      maxLength: this.textFieldParam.max || 1000,
      title: data.comment_label,
      translations: this.translations,
    })

    this.commentLabel = data.comment_label || 'Ваш комментарий'

    this.isValid = computed(() => {
      if (this.skip && this.skipped.value)
        return true
      if (this.isRequired.value && this.stars.value <= 0)
        return false
      if (this.hasComment.value && !toValue(this.commentController.isValid))
        return false
      if (this.isClarifyingQuestionVisible.value && !toValue(this.variantsController.isValid))
        return false
      return true
    })

    this.blocked = computed(() => {
      return this.touched.value && !this.isValid.value
    })

    this.detailAnswers = data.detail_answers || []

    this.variantsController = new VariantsController({
      isRequired: this.extraRequired.value,
      enabled: computed(() => this.isClarifyingQuestionVisible.value),
      label: this.answerText.value,
      previousAnswerItems: JSON.parse(this.previousAnswer?.detail_item || '[]'),
      previousAnswerHasSelfVariant: this.previousAnswer?.is_self_variant === 1,
      textFieldValue: this.previousAnswer?.self_variant || '',
      variants: this.detailAnswers.map(v => ({
        ...v,
        isChecked: false,
      })),
      variantsType: data.variantsType,
      textFieldParam: this.textFieldParam,
      placeholderText: this.placeholderText,
      translations: this.translations,
      hasCustomField: data.isHaveCustomField,
      dropdown: data.dropdownVariants,
      selfVariantText: data.self_variant_text,
      selfVariantNothing: data.self_variant_nothing === 1,
      selfVariantCommentRequired: true,
      selfVariantMinLength: this.textFieldParam.min || 0,
      selfVariantMaxLength: this.textFieldParam.max || 1000,
      selfVariantPlaceholderText: data.placeholderText,
      isCustomFieldChecked: data.isCustomFieldChecked,
      skipped: this.skipped,
    })

    watch(this.stars, (newValue) => {
      if (newValue > 0 && this.skip) {
        this.skipped.value = false
      }
    })

    watch(() => this.variantsController.isValid.value, (isValid) => {
      if (isValid) {
        this.emit('get-answer')
      }
    })

    watch(this.skipped, (newValue) => {
      if (newValue) {
        this.resetFields()
        this.markInteracted()
        this.emit('get-answer')
      }
    })

    /**
     * Флаг, который отвечает за то, может ли пользователь перейти к следующему вопросу
     * Используется при постраничном разделении и включенном автоскролле страниц
     * Если пользователь ответил на вопрос, то при определенных условиях
     * он может перейти к следующему вопросу
     */
    this.canMoveToNextQuestion = ref(false)

    watch([this.stars, this.isClarifyingQuestionVisible, this.skipped], ([starsValue, isClarifyingQuestionVisible, skipped]) => {
      if (skipped) {
        // Если пользователь пропустил вопрос, то он может перейти к следующему вопросу
        this.canMoveToNextQuestion.value = true
        return
      }

      if (!starsValue) {
        // Если пользователь не поставил оценку, то он не может перейти к следующему вопросу
        this.canMoveToNextQuestion.value = false
        return
      }

      if (this.hasComment.value) {
        // Если в вопросе есть поле для комментария, то пользователь не может перейти к следующему вопросу
        this.canMoveToNextQuestion.value = false
        return
      }

      if (!isClarifyingQuestionVisible) {
        // Если вопрос и оценка не содержит уточняющего вопроса,
        // то пользователь может перейти к следующему вопросу
        // (если оценка больше 0)
        this.canMoveToNextQuestion.value = starsValue > 0
      }
      else {
        this.canMoveToNextQuestion.value = false
      }
    })

    watch(this.variantsController.answers, (answers) => {
      const isSelfVariantChecked = Array.isArray(answers) ? answers.includes('is_self_answer') : answers === 'is_self_answer'

      if (this.variantsController.isSingle && !isSelfVariantChecked && this.isClarifyingQuestionVisible.value && !tabletStore.isTabletMode) {
        this.canMoveToNextQuestion.value = false
      }
    })

    this.selectedVariants = computed(() => {
      return [this.stars.value]
    })
  }

  resetFields() {
    this.touched.value = false
    this.err.value = null
    this.stars.value = 0
    this.comment.value = ''
    this.commentController.touched.value = false
    this.commentController.error.value = null
  }

  setAllAsTouched() {
    this.touched.value = true

    if (this.hasComment.value) {
      this.commentController.touched.value = true
    }
    if (this.isClarifyingQuestionVisible.value) {
      this.variantsController.touched.value = true
      if (this.variantsController.isSelfVariantChecked.value) {
        this.variantsController.selfVariantCommentTouched.value = true
      }
    }
  }

  checkValidity() {
    if (this.skip && this.skipped.value)
      return true
    if (this.hasComment.value && this.isCommentRequired && !this.commentController.isValid.value)
      return false
    if (!this.starsValidation.value)
      return false

    if (this.isClarifyingQuestionVisible.value && !this.variantsController.isValid.value)
      return false
    return true
  }

  validateRating() {
    this.err.value = null
    if (!this.touched.value)
      return true

    if (!this.isRequired.value) {
      this.err.value = null
      return true
    }

    if (this.skip && this.skipped.value) {
      this.err.value = null
      return true
    }

    if (this.stars.value <= 0) {
      this.err.value = this.t('Нужно поставить оценку')
      return false
    }

    if (this.stars.value > 0) {
      this.err.value = null
      return true
    }
  }

  validate() {
    this.validateRating()
    if (this.hasComment.value)
      this.commentController.validate()
    if (this.isClarifyingQuestionVisible.value)
      this.variantsController.validate()

    return this.checkValidity()
  }

  getData() {
    if (this.skip && this.skipped.value) {
      return { skipped: 1 }
    }

    const data = {}

    if (this.stars.value > 0) {
      data.rating = this.stars.value
    }

    if (this.hasComment.value) {
      data.answer = toValue(this.commentController.value)
    }

    if (this.isClarifyingQuestionVisible.value) {
      const variantsData = this.variantsController.getData()
      data.detail_item = variantsData.detail_item
      if (variantsData.answer) {
        data.self_variant = variantsData.answer
      }
      if (variantsData.textAnswer) {
        data.answer = variantsData.textAnswer
      }
      if (variantsData.self_variant) {
        data.self_variant = variantsData.self_variant
      }
    }

    return data
  }

  getAnswer() {
    const count = this.stars.value

    if (!count)
      return ''

    return `${count} ${declOfNum(count, ['звезда', 'звезды', 'звезд'])}`
  }

  get hasValue() {
    if (this.skip && this.skipped.value)
      return true
    const hasPreviousRating = this.previousAnswer?.rating !== undefined
    return hasPreviousRating || this.stars.value > 0
  }

  checkCondition(condition) {
    // First check parent class conditions
    if (super.checkCondition(condition))
      return true

    // Get current rating value from reactive stars variable
    const rating = this.stars.value

    switch (condition.behavior) {
      case BEHAVIOR_MISS:
        // Check if question was skipped or has no rating
        return this.skipped.value || rating <= 0

      case BEHAVIOR_SELECT:
        // Check if current rating is in selected variants
        return condition.variants.includes(rating)

      case BEHAVIOR_UNSELECT:
        // Check if current rating is NOT in selected variants
        return !condition.variants.includes(rating)

      case BEHAVIOR_ALWAYS:
        return true

      default:
        return false
    }
  }

  /**
   * Resolves view logic condition for star rating question
   * @param {object} rule - The view logic rule to check
   * @returns {boolean} Whether the condition is met
   */
  resolveViewLogic(rule) {
    const rating = this.stars.value
    if (!rating)
      return false

    // Check if current rating is in the required variants
    return rule.variants?.includes(rating) ?? false
  }

  getStringifiedAnswer() {
    return computed(() => {
      if (this.skip && this.skipped.value)
        return ''

      const count = this.stars.value
      if (!count)
        return ''

      const words = ['звезда', 'звезды', 'звезд']
      const declWord = declOfNum(count, words)
      const translated = this.t(`{count} ${declWord}`, { count })
      return toValue(translated)
    })
  }

  updateFromPreview(previewData) {
    const preparedData = this.preparePreviewData(previewData)

    // Call parent update first
    super.updateFromPreview(preparedData)

    // Update stars configuration
    if (preparedData.starRatingOptions) {
      this.extraQuestionRateFrom.value = preparedData.starRatingOptions.extra_question_rate_from
      this.extraQuestionRateTo.value = preparedData.starRatingOptions.extra_question_rate_to
      // Merge existing config with new values to preserve reactivity
      this.starsConfig.value = {
        ...this.starsConfig.value,
        ...preparedData.starRatingOptions,
      }
    }

    // Update clarifying question fields
    if (preparedData.clarifyingQuestionRequired !== undefined) {
      this.extraRequired.value = preparedData.clarifyingQuestionRequired
    }
    if (preparedData.clarifyingQuestionEnabled !== undefined) {
      this.isClarifyingQuestionEnabled.value = preparedData.clarifyingQuestionEnabled
    }
    if (preparedData.answerText !== undefined) {
      this.answerText.value = preparedData.answerText
    }

    if (preparedData.forAllRates !== undefined) {
      this.forAllRates.value = preparedData.forAllRates
    }

    // Update skip-related fields
    const skipIsChanged = preparedData.skip !== undefined && !!preparedData.skip !== !!this.skip.value

    if (skipIsChanged) {
      this.skip.value = preparedData.skip
      if (!this.skip.value) {
        this.skipped.value = false
      }
    }

    if (preparedData.skip_text !== undefined) {
      this._skipText.value = preparedData.skip_text
    }

    // Update display options
    if (preparedData.show_labels !== undefined) {
      this.showLabels.value = preparedData.show_labels
    }
    if (preparedData.show_numbers !== undefined) {
      this.showNumbers.value = preparedData.show_numbers
    }

    // Update comment fields
    if (preparedData.comment?.enabled !== undefined) {
      this.hasComment.value = preparedData.comment.enabled
    }
    // Update comment fields
    if (this.hasComment.value && this.commentController) {
      this.commentController.updateFromPreview(preparedData.comment || {})
    }

    // Update stars value if provided in preview data
    const starsAreSet = this.stars.value > 0
    const countIsLessThanStars = this.starsConfig.value.count < this.stars.value
    if (countIsLessThanStars && starsAreSet) {
      this.stars.value = this.starsConfig.value.count
    }

    // Update gallery if enabled

    this.enableGallery.value = preparedData.enableGallery

    if (toValue(this.enableGallery) && this.galleryController) {
      this.galleryController.updateFromPreview({
        enabled: toValue(this.enableGallery),
        gallery: preparedData.gallery || [],
      })
    }

    this.variantsController.updateFromPreview({
      isRequired: this.extraRequired.value,
      variantsType: preparedData.variantsType,
      hasCustomField: preparedData.isHaveCustomField,
      label: preparedData.answerText,
      variants: preparedData.variants || [],
      textFieldParam: preparedData.textFieldParam || { min: 0, max: 999 },
      placeholderText: preparedData.placeholderText,
      dropdown: preparedData.dropdownVariants,
      selfVariantText: preparedData.self_variant_text,
      selfVariantPlaceholderText: preparedData?.comment?.placeholderText,
    })
  }

  get hasGallery() {
    return toValue(this.enableGallery) && this.galleryController.gallery.value.length > 0
  }

  /**
   * Преобразует данные из превью в формат, ожидаемый моделью вопроса со звездным рейтингом
   * @param {object} previewData - Данные из превью
   * @returns {object} Преобразованные данные
   */
  preparePreviewData(previewData) {
    return prepareStarsQuestionPreviewData(previewData)
  }
}
