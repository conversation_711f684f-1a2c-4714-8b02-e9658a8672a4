// #if VITE_USE_SENTRY
import * as Sentry from '@sentry/vue'
// #endif
import { getBaseApiUrl } from '@shared/api'

/**
 * Получает данные опроса
 * @param {string} key - Ключ опроса
 * @param {object} [opts] - Дополнительные параметры
 * @param {string} [opts.lang] - Язык опроса
 * @param {string} [opts.pollId] - ID опроса
 * @param {number} [opts.edit] - Флаг режима редактирования (1 - включен)
 * @param {string} [opts.view] - ID вопроса для просмотра
 * @param {boolean} [opts.tabletMode] - Флаг режима планшета
 * @returns {Promise<object>} Данные опроса
 * @throws {Error} Если не удалось получить данные опроса
 */
export async function fetchPollData(key, opts = {}) {
  const url = new URL(window.location.href)
  const searchParams = url.searchParams

  // take all query params and append them to the form data as 'params'
  const params = {}
  searchParams.forEach((value, key) => {
    // ignore 'lang' param
    if (key === 'lang') {
      return
    }
    params[key] = value
  })

  const formData = new FormData()
  Object.entries(params).forEach(([key, value]) => {
    formData.append(`params[${key}]`, value)
  })

  const apiEndpoint = import.meta.env.VITE_API_ENDPOINT || '/foquz/api/p'
  let pollDataUrl = `${getBaseApiUrl()}${apiEndpoint}/answer?lang=${opts.lang || ''}`

  if (opts.pollId) {
    pollDataUrl += `&pollId=${opts.pollId}`
  }
  else {
    pollDataUrl += `&key=${key}`
  }

  if (opts.isPreview) {
    pollDataUrl += '&preview'
  }

  // Add edit mode parameter if provided
  if (opts.edit === 1) {
    pollDataUrl += '&edit=1'
  }

  // Add view question parameter if provided
  if (opts.view) {
    pollDataUrl += `&view=${opts.view}`
  }

  // Add kiosk parameter if tablet mode is enabled
  if (opts.tabletMode) {
    pollDataUrl += '&kiosk=1'
  }

  const response = await fetch(pollDataUrl, { method: 'POST', body: formData })

  if (!response.ok) {
    const errorText = await response.text()
    let errorMessage = errorText
    if (errorText.includes('Поле key обязательно')) {
      errorMessage = 'Опрос не найден'
    }
    else if (errorText.includes('Опрос не найден')) {
      errorMessage = 'Опрос не найден'
    }
    const error = new Error(errorMessage)
    // #if VITE_USE_SENTRY
    Sentry.captureException(error, {
      extra: {
        msg: 'Failed to fetch poll data',
        key,
        lang: opts.lang,
        params,
      },
    })
    // #endif
    throw error
  }

  const pollData = await response.json()

  return pollData
}

/**
 * Сохраняет ответ пользователя
 * @param {object} opts - Параметры ответа
 * @param {string} opts.authKey - Ключ авторизации
 * @param {string?} opts.questionId - Идентификатор вопроса
 * @param {string} opts.item - Ответ пользователя
 * @param {object} opts.items - Ответы пользователя
 * @param {string[]} opts.skippedQuestionsIds - Идентификаторы пропущенных вопросов.
 * Вопросы могут быть пропущены по логическим переходам
 * @param {boolean} opts.editMode - Флаг режима редактирования
 * @param {boolean} opts.tabletMode - Флаг режима планшета
 * @returns {Promise<object>} Результат сохранения
 * @throws {Error} Если не удалось сохранить ответ
 */
export async function saveAnswer(opts) {
  const formData = new FormData()

  // Если переданы несколько ответов, то добавляем их в форму
  if (opts.items) {
    Object.entries(opts.items).forEach(([questionId, item]) => {
      formData.append(`FoquzAnswerItem[${questionId}]`, item)
    })
  }
  else if (opts.item) {
    // Сохраняем ответ для одного вопроса
    formData.append('FoquzAnswerItem', opts.item)
  }

  const apiEndpoint = import.meta.env.VITE_API_ENDPOINT || '/foquz/api/p'
  const url = new URL(`${getBaseApiUrl()}${apiEndpoint}/save-answer`)
  url.searchParams.append('authKey', opts.authKey)
  if (opts.questionId) {
    url.searchParams.append('questionId', opts.questionId)
  }
  if (opts.skippedQuestionsIds?.length > 0) {
    opts.skippedQuestionsIds.forEach((questionId) => {
      formData.append('hiddenQuestions[]', questionId)
    })
  }

  // Add edit mode parameter if in edit mode
  if (opts.editMode) {
    url.searchParams.append('edit', '1')
  }

  // Add kiosk parameter if tablet mode is enabled
  if (opts.tabletMode) {
    url.searchParams.append('kiosk', '1')
  }

  const response = await fetch(url.toString(), {
    method: 'POST',
    body: formData,
  })

  if (!response.ok) {
    const errorText = await response.text()
    const error = new Error(errorText)
    // #if VITE_USE_SENTRY
    Sentry.captureException(error, {
      extra: {
        msg: 'Failed to save answer',
        questionId: opts.questionId,
        items: opts.items,
        item: opts.item,
      },
    })
    // #endif
    return { error: errorText }
  }
  return response.json()
}

/**
 * Обновляет статус опроса
 * @param {object} params - Параметры обновления
 * @param {string} params.authKey - Ключ авторизации
 * @param {string} params.status - Новый статус опроса
 * @returns {Promise<object>} Результат обновления
 * @throws {Error} Если не удалось обновить статус опроса
 */
export async function updatePollStatus({ authKey, status }) {
  const formData = new FormData()
  formData.append('status', status)
  formData.append('authKey', authKey)

  const apiEndpoint = import.meta.env.VITE_API_ENDPOINT || '/foquz/api/p'
  const url = `${getBaseApiUrl()}${apiEndpoint}/change-status?authKey=${authKey}`

  const response = await fetch(url, {
    method: 'POST',
    body: formData,
  })

  if (!response.ok) {
    throw new Error('Failed to update poll status')
  }

  const data = await response.json()
  return data
}

/**
 * Загружает файлы для конкретного вопроса в опросе
 * @param {object} opts - Параметры загрузки
 * @param {string} opts.authKey - Ключ авторизации
 * @param {string} opts.questionId - Идентификатор вопроса
 * @param {File[]} opts.files - Массив файлов для загрузки
 * @param {boolean} opts.isPreview - Флаг режима предпросмотра
 * @returns {Promise<object>} Ответ от сервера
 * @throws {Error} Если не удалось загрузить файлы
 */
export async function uploadFiles({ authKey, questionId, files, isPreview = false }) {
  const formData = new FormData()

  // Append each file to the FormData
  files.forEach((file) => {
    formData.append(`files[]`, file)
  })

  const apiEndpoint = import.meta.env.VITE_API_ENDPOINT || '/foquz/api/p'
  const url = new URL(`${getBaseApiUrl()}${apiEndpoint}/upload-files`)
  url.searchParams.append('authKey', authKey)
  url.searchParams.append('question_id', questionId)
  if (isPreview) {
    url.searchParams.append('preview', '1')
  }

  const response = await fetch(url.toString(), {
    method: 'POST',
    body: formData,
  })

  if (!response.ok) {
    const error = new Error('Failed to upload files')
    error.status = response.status
    throw error
  }

  return response.json()
}

/**
 * Отписывает пользователя от опроса
 * @param {string} authKey - Ключ авторизации
 * @returns {Promise<object>} Результат отписки
 * @throws {Error} Если не удалось отписаться
 */
export async function unsubscribe(authKey) {
  if (!authKey) {
    return
  }

  const formData = new FormData()
  formData.append('authKey', authKey)

  const apiEndpoint = import.meta.env.VITE_API_ENDPOINT || '/foquz/api/p'
  const response = await fetch(`${getBaseApiUrl()}${apiEndpoint}/unsubscribe`, {
    method: 'POST',
    body: formData,
  })

  if (!response.ok) {
    throw new Error('Failed to unsubscribe')
  }

  return await response.json()
}

// @TODO: Uncomment when API is ready
/**
 * Запускает таймер опроса
 * @param {string} authKey - Ключ авторизации
 * @returns {Promise<object>} Результат запуска таймера
 * @throws {Error} Если не удалось запустить таймер
 */
export async function setupPollTimer(authKey) {
  const apiEndpoint = import.meta.env.VITE_API_ENDPOINT || '/foquz/api/p'
  const url = new URL(`${getBaseApiUrl()}${apiEndpoint}/setup-timer`)
  url.searchParams.append('key', authKey)

  const response = await fetch(url.toString(), {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ authKey }),
  })

  if (!response.ok) {
    throw new Error('Failed to setup timer')
  }

  return response.json()
}

/**
 * Получает обновленные переменные после ответа на вопросы
 * @param {string} authKey - Ключ авторизации
 * @returns {Promise<object>} Обновленные переменные
 */
export async function fetchAnswerVariables(authKey) {
  const apiEndpoint = import.meta.env.VITE_API_ENDPOINT || '/foquz/api/p'
  const url = new URL(`${getBaseApiUrl()}${apiEndpoint}/answer-variables`)
  url.searchParams.append('key', authKey)

  const response = await fetch(url.toString())

  if (!response.ok) {
    throw new Error('Failed to fetch answer variables')
  }

  return response.json()
}
