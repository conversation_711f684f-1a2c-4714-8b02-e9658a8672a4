<script setup>
import { POLL_NAVIGATION_TYPE } from '@/shared/constants'
import { usePollStore } from '@entities/poll/model/store'
import { usePointsStore } from '@features/points/store/pointsStore'
import PointsReportScreen from '@features/points/ui/PointsReportScreen.vue'
import { useScrollShadows } from '@shared/composables/useScrollShadows'
import { updateCSSVariables } from '@shared/helpers/ui'
import { useTranslationsStore } from '@shared/store/translationsStore'
import { useSimplifiedStore } from '@shared/store/useSimplifiedStore'
import { useTabletStore } from '@shared/store/useTabletStore'
import FoquzBanner from '@shared/ui/FoquzBanner.vue'
import InfoScreen from '@shared/ui/InfoScreen.vue'
import Loader from '@shared/ui/Loader.vue'
import { StickyContainer } from '@shared/ui/StickyContainer'
import UserConsentBanner from '@shared/ui/UserConsentBanner.vue'
import WidgetTopline from '@shared/ui/WidgetTopline.vue'
import { useHead } from '@unhead/vue'
import { createReusableTemplate } from '@vueuse/core'
import CoverBanner from '@widgets/ui/CoverBanner.vue'
import Footer from '@widgets/ui/Footer.vue'
import Header from '@widgets/ui/Header.vue'
import PollBody from '@widgets/ui/PollBody.vue'
import PollContent from '@widgets/ui/PollContent.vue'
import PollPaginator from '@widgets/ui/PollPaginator.vue'
import { storeToRefs } from 'pinia'
import { computed, onMounted, onUnmounted, provide, toValue, useTemplateRef, watch } from 'vue'
import { getBaseAssetsUrl } from '../shared/api'
import { usePreviewStore } from '../shared/store/previewStore'
import SlideTransition from '../shared/ui/SlideTransition.vue'
import FadeTransition from '../shared/ui/transitions/FadeTransition.vue'

import PollActions from '../widgets/ui/PollActions.vue'
import QuestionPagesContainer from '../widgets/ui/QuestionPagesContainer.vue'
import 'simplebar-vue/dist/simplebar.min.css'

const pageTopline = useTemplateRef('pageTopline')
const pageTop = useTemplateRef('pageTop')
provide('refPageTop', computed(() => pageTop.value || pageTopline.value))

const store = usePollStore()
const previewStore = usePreviewStore()
const translationsStore = useTranslationsStore()
const pointsStore = usePointsStore()
const simplifiedStore = useSimplifiedStore()
const tabletStore = useTabletStore()

const { showTimer, timeToPass, timer } = storeToRefs(store)

const key = window.location.pathname.split('/').pop()

// Получаем сегмент пути, который указывает на режим превью
const isPreviewMode = window.location.pathname.includes('/preview')

const params = new URLSearchParams(window.location.search)
const lang = params.get('lang')
const pollKey = params.get('pollKey')
const activeQuestionId = params.get('questionId')
const isDesignPreview = params.get('designPreview') === '1' || params.get('design-preview') === '1'
const isFullPreview = params.get('fullPreview') === '1' || params.get('full-preview') === '1'
const isWidgetPreviewMode = params.get('preview-mode') === '1'
const pollId = params.get('pollId')
const shouldEnableSimplifiedMode = params.get('simple') === '1'
// Get edit mode and view question parameters
const isEditMode = params.get('edit') === '1'
const viewQuestionNumber = params.get('view')

onMounted(() => {
  if (shouldEnableSimplifiedMode) {
    simplifiedStore.initialize()
  }

  simplifiedStore.initializeCloseByFinishButton()
  simplifiedStore.initializeWidgetMode()

  // Always initialize tablet mode to check URL path
  tabletStore.initialize()
  previewStore.setIsWidgetPreviewMode(isWidgetPreviewMode)

  if (isPreviewMode) {
    previewStore.setPreviewMode(true)
    previewStore.setIsDesignPreview(isDesignPreview)
    previewStore.setIsFullPreview(isFullPreview)

    // Apply overflow hidden to body when in preview mode (not full preview)
    if (!isFullPreview) {
      document.body.style.overflow = 'hidden'
    }

    // Инициализируем превью опроса
    store.initializePollPreview()
    store.fetchPoll(pollKey, lang, activeQuestionId, pollId)
  }
  else {
    // Получаем опрос по ключу
    store.fetchPoll(key, lang, activeQuestionId, pollId, isEditMode, viewQuestionNumber)
  }
})

// Cleanup overflow style when component is unmounted
onUnmounted(() => {
  document.body.style.overflow = ''
})

useHead({
  title: () => store.pollTitle || 'Foquz',
  meta: [
    { name: 'description', content: () => store.pollDescription },
  ],
})

// @NOTE: Обновляем CSS переменные при изменении дизайна
watch(() => store.design, (newDesign) => {
  if (Object.keys(newDesign).length > 0) {
    updateCSSVariables(document.documentElement, newDesign)
  }
}, { deep: true })

const isPollBlocked = computed(() => {
  if (store.fetchPollError) {
    return true
  }

  const isPollArchived = store.isPollArchived
  const isPollPeriodOver = store.isPollPeriodOver
  const isAnswersLimitsOver = store.isAnswersLimitsOver
  const isCompanyAnswersLimitsOver = store.isCompanyAnswersLimitsOver
  const isPollActive = store.isPollActive
  const isTestModeLimitOver = store.isTestModeLimitOver
  const isTimerFinished = timer.value.enabled && timer.value.status === 'finished'
  const isSaveAnswerError = store.saveAnswerError
  const showQuoteFullInfoScreen = store.showQuoteFullInfoScreen
  const isResponseTimeExpired = store.isResponseTimeExpired

  return isPollArchived
    || isPollPeriodOver
    || isAnswersLimitsOver
    || isCompanyAnswersLimitsOver
    || !isPollActive
    || isTestModeLimitOver
    || isTimerFinished
    || isSaveAnswerError
    || showQuoteFullInfoScreen
    || isResponseTimeExpired
})

const shouldShowPaginator = computed(() => {
  if (simplifiedStore.isSimplifiedMode)
    return false
  const startOrEndPage = store.isStartPage || store.isEndPage
  if (startOrEndPage) {
    return false
  }
  const isNavigationEnabled = store.showNavigationType !== POLL_NAVIGATION_TYPE.DISABLED

  return isNavigationEnabled && !isPollBlocked.value
})

const shouldShowNumberPaginator = computed(() => {
  const isNumberPaginationEnabled = store.showNavigationType === POLL_NAVIGATION_TYPE.NUMBERS
  return shouldShowPaginator.value && isNumberPaginationEnabled
})

const shouldShowProgressbarPaginator = computed(() => {
  const isProgressbarPaginationEnabled = store.showNavigationType === POLL_NAVIGATION_TYPE.PROGRESSBAR
  return shouldShowPaginator.value && isProgressbarPaginationEnabled
})

const isFoquzBannerVisible = computed(() => {
  const isBannerEnabled = store.showAdvertisementBanner
  const isEndPage = store.isEndPage
  if (isBannerEnabled && isPollBlocked.value && !isPreviewMode) {
    return true
  }
  else if (isBannerEnabled && isEndPage) {
    return true
  }
  return false
})

const showFooter = computed(() => {
  if (pointsStore.isPrintMode || simplifiedStore.isSimplifiedMode)
    return false
  const pollLoadedWithoutErrors = !store.isLoading && !store.fetchPollError
  const showFoquzLink = store.showFoquzLink
  return pollLoadedWithoutErrors && showFoquzLink
})

const bannerTitle = computed(() => toValue(translationsStore.t('Создайте свой опрос в FOQUZ')))
const bannerSubtitle = computed(() => toValue(translationsStore.t('Демо-доступ на 1 месяц и до 50 собранных анкет')))
const bannerButtonText = computed(() => toValue(translationsStore.t('Создать')))

const showHeaderComponent = computed(() => {
  if (store.isLoading) {
    return false
  }

  if (store.fetchPollError) {
    return true
  }

  let show = store.design && !!store.design?.is_use_header

  // Если шапка выключена, проверяем наличие тестового режима
  // При включенном тестовом режиме компонент шапки все равно должен отображаться
  // но контент в нем будет: "Тестовый режим" + Выбор языка (если языков больше 1)
  if (!show) {
    show = store.testMode
  }
  return show
})

const appRootContainerClasses = computed(() => {
  const classes = {
    'app-root-container': true,
    'app-root-container--simplified-iframe': simplifiedStore.isInIframe,
  }

  if (isPreviewMode && !simplifiedStore.isSimplifiedMode) {
    classes['simplebar-custom-fullpage'] = true
  }

  if (isPreviewMode && !isFullPreview && !simplifiedStore.isSimplifiedMode) {
    classes['app-root-container--non-full-preview'] = true
  }

  return classes
})

const showAppRootTop = computed(() => {
  if (simplifiedStore.isSimplifiedMode) {
    return false
  }
  return !store.isLoading
})

const headerAttrs = computed(() => {
  const baseUrl = getBaseAssetsUrl()
  const logoImage = store.design?.logo_image?.startsWith?.('/') ? `${baseUrl}${store.design?.logo_image}` : store.design?.logo_image
  const attrs = {
    'logo-link': store.design?.logo_link,
    'disabled': !store.design?.is_use_header,
    'logo-type': store.design?.logo_type,
    'logo-text': store.design?.logo_text,
    'logo-font-family': store.design?.logo_font_family,
    'logo-color': store.design?.logo_color,
    'logo-position': store.design?.logo_position,
    'logo-margins': store.design?.logo_margins,
    'small-header-mobile': store.design?.small_header_mobile,
    'logo-height': store.design?.logo_height,
    'logo-text-size': store.design?.logo_text_size,
    'logo-text-bold': store.design?.logo_text_bold,
    'logo-text-italic': store.design?.logo_text_italic,
    'logo-image': logoImage,
    'show-testmode-label': store.testMode,
    'sticky': true,
  }

  return attrs
})

const paginatorAttrs = computed(() => ({
  'active-page-id': store.activePageId,
  'type': store.showNavigationType,
  'pages': store.pages,
  'active-page-index': store.activePageIndex,
  'pages-mode': true,
  'questions': store.questions,
  'is-header-enabled': store.design?.is_use_header,
  'progress-percentage': store.progressPercentage,
  'interacted-questions-count': store.interactedQuestionsCount,
  'visible-questions-count': store.visibleQuestionsCount,
}))

const showUserConsentBanner = computed(
  () => {
    if (simplifiedStore.isSimplifiedMode) {
      return false
    }
    return store.showUserConsentBanner && store.pollStatus === 'open' && !isPollBlocked.value
  },
)

const shouldEnableShadows = computed(() =>
  simplifiedStore.isSimplifiedMode && !simplifiedStore.isInIframe,
)

const { showShadowTop, showShadowBottom } = useScrollShadows(
  window,
  shouldEnableShadows,
)

const pollContentClasses = computed(() => ({
  'poll-content': true,
  'poll-content--padding-top': !shouldShowPaginator.value && !store.isStartPage && !store.isEndPage,
  'poll-content--simplified': simplifiedStore.isSimplifiedMode,
  'poll-content--simplified-iframe': simplifiedStore.isSimplifiedMode && simplifiedStore.isInIframe,
}))

const [DefineTemplate, ReuseTemplate] = createReusableTemplate()

const appRootClasses = computed(() => ({
  'app-root': true,
  'app-root--is-loading': store.isLoading,
  'app-root--print': pointsStore.isPrintMode,
  'app-root--simplified': simplifiedStore.isSimplifiedMode,
  'app-root--simplified-iframe': simplifiedStore.isSimplifiedMode && simplifiedStore.isInIframe,
  'app-root--simplified-auto-height': simplifiedStore.isAutoHeight,
  'app-root--simplified-hello-board-widget': simplifiedStore.isHelloBoardWidget,
  'app-root--tablet': tabletStore.isTabletMode,
}))

const showHeader = computed(() => {
  if (pointsStore.isPrintMode || simplifiedStore.isSimplifiedMode)
    return false
  return showHeaderComponent.value
})

const showFoquzBanner = computed(() => {
  if (pointsStore.isPrintMode || simplifiedStore.isSimplifiedMode)
    return false
  return isFoquzBannerVisible.value
})

const showPollContent = computed(() => {
  if (!store.pages || store.fetchPollError || store.isLoading || isPollBlocked.value) {
    return false
  }

  return true
})

const showWidgetTopline = computed(() => {
  if (simplifiedStore.isSimplifiedMode) {
    return true
  }
  return false
})

const widgetToplineClasses = computed(() => ({
  'app-root__widget-topline': true,
  'fqz-shadow-top--active': showShadowTop.value,
  'fqz-shadow-top': shouldEnableShadows.value,
  'fqz-shadow-top-outer': shouldEnableShadows.value,
}))

const pollActionsClasses = computed(() => ({
  'app-root__poll-actions': true,
  'fqz-shadow-bottom-outer': shouldEnableShadows.value,
  'fqz-shadow-bottom': shouldEnableShadows.value,
  'fqz-shadow-bottom--active': showShadowBottom.value,
}))

const isUseCover = computed(() => {
  const {
    in_use_cover,
    cover_image,
    cover_only_first_page,
  } = store.design || {}
  if (!in_use_cover || !cover_image)
    return false

  const isFirstPage = store.activePageIndex === 0
  return !cover_only_first_page || isFirstPage
})

const isUseCoverInSimplifiedMode = computed(() => {
  if (isUseCover.value && simplifiedStore.isSimplifiedMode) {
    return true
  }
  return false
})

const coverBannerClasses = computed(() => ({
  'app-root__cover-banner': true,
  'app-root__cover-banner--widget-topline-visible': showWidgetTopline.value,
}))
const shouldShowPollUnavailable = computed(() => {
  const show = (store.isPollArchived || !store.isPollActive) && !store.isPollPeriodOver
  return show
})
</script>

<template>
  <DefineTemplate>
    <div :class="appRootClasses">
      <StickyContainer v-if="showWidgetTopline">
        <WidgetTopline
          ref="pageTopline"
          :class="widgetToplineClasses"
          :show-close-button="simplifiedStore.isInIframe && !simplifiedStore.isHelloBoardWidget"
          :show-foquz-label="store.showFoquzLabel"
          :is-above-all="store.isLoading"
          @close="simplifiedStore.closeWidget"
        />
      </StickyContainer>

      <div class="app-root__covering" aria-hidden="true" />

      <SlideTransition :skip-initial-animation="true">
        <div v-if="isUseCoverInSimplifiedMode" :class="coverBannerClasses">
          <CoverBanner :show-header="showHeader" />
        </div>
      </SlideTransition>

      <div
        v-if="showAppRootTop"
        ref="pageTop"
        class="app-root__top"
      >
        <UserConsentBanner v-if="showUserConsentBanner" />
        <StickyContainer v-if="showHeader">
          <Header v-bind="headerAttrs" />
        </StickyContainer>
        <SlideTransition>
          <div v-if="isUseCover" :class="coverBannerClasses">
            <CoverBanner :show-header="showHeader" />
          </div>
        </SlideTransition>
        <StickyContainer
          v-if="shouldShowProgressbarPaginator"
          class="app-root-container__paginator"
        >
          <PollPaginator
            v-bind="paginatorAttrs"
          />
        </StickyContainer>
      </div>

      <PollPaginator
        v-if="shouldShowNumberPaginator"
        class="app-root-container__paginator app-root-container__paginator_number"
        v-bind="paginatorAttrs"
      />
      <FadeTransition>
        <Loader v-if="store.isLoading" view="default" />
      </FadeTransition>
      <FadeTransition>
        <div v-if="!store.isLoading && isPollBlocked" class="app-root__info-screen-container">
          <template v-if="(timer.enabled && timer.status === 'finished') || store.isResponseTimeExpired">
            <InfoScreen type="timer-is-up" />
          </template>
          <template v-else-if="shouldShowPollUnavailable">
            <InfoScreen type="poll-unavailable" />
          </template>
          <template v-else-if="store.isPollPeriodOver">
            <InfoScreen type="poll-period-over" />
          </template>
          <template v-else-if="store.showQuoteFullInfoScreen">
            <InfoScreen type="quota-limit-over" />
          </template>
          <template v-else-if="store.saveAnswerError">
            <InfoScreen type="save-answer-error" />
          </template>
          <template v-else-if="store.isCompanyAnswersLimitsOver || store.isAnswersLimitsOver">
            <InfoScreen type="poll-unhandled-error" />
          </template>
          <template v-else-if="store.fetchPollError">
            <InfoScreen type="poll-server-error" :text="store.fetchPollError.cause?.error" :error="store.fetchPollError" />
          </template>
          <template v-else-if="store.isTestModeLimitOver">
            <InfoScreen type="test-mode-limit-over" />
          </template>
        </div>
      </FadeTransition>
      <PollContent v-if="showPollContent" :class="pollContentClasses">
        <template #default>
          <PollBody>
            <QuestionPagesContainer />
            <PollActions
              v-if="!store.isStartPage && !store.isEndPage" :is-tablet="tabletStore.isTabletMode"
              :page-index="store.activePageIndex" :is-sticky="true" :is-first="store.isFirstPage"
              :should-show-finish-button="store.shouldShowFinishButton" :finish-link="store.finishLink"
              :back-button-text="store.backButtonText" :next-button-text="store.forwardButtonText"
              :time-to-pass="timeToPass" :show-timer="showTimer" :time-left="timer.timeLeft"
              :is-submitted="store.isSubmitted"
              :has-prev="store.showPrevButton" :page-is-valid="store.activePage?.isValid"
              :additional-margin-bottom="store.showFoquzLink ? 0 : 10"
              :class="pollActionsClasses"
            />
          </PollBody>
        </template>
      </PollContent>
      <Footer v-if="showFooter" :show-copyright="store.showFoquzLink" />
      <FadeTransition>
        <div v-if="showFoquzBanner" class="app-root__foquz-banner">
          <FoquzBanner
            :title="bannerTitle" :subtitle="bannerSubtitle" :button-text="bannerButtonText"
          />
        </div>
      </FadeTransition>
    </div>
  </DefineTemplate>
  <div v-if="pointsStore.isPrintMode" class="print-report-container">
    <PointsReportScreen
      :results="pointsStore.formattedResultsData"
      :loading="pointsStore.isPrintLoading"
      back-button-text="Назад"
      :show-buttons="false"
      view="print"
    />
  </div>
  <simplebar
    v-else-if="previewStore.isPreviewMode && !previewStore.isFullPreview && !simplifiedStore.isSimplifiedMode"
    :class="appRootContainerClasses"
    :class-names="{
      content: 'simplebar-content fz-simplebar-content',
      track: 'simplebar-track fz-simplebar-track',
      contentWrapper: 'simplebar-content-wrapper fz-simplebar-content-wrapper',
    }"
    data-simplebar-auto-hide="true"
  >
    <ReuseTemplate />
  </simplebar>
  <div v-else :class="appRootContainerClasses">
    <ReuseTemplate />
  </div>
</template>

<style scoped>
.app-root-container__paginator {
  margin-bottom: 35px;
}
.app-root-container__paginator_number {
  margin-top: 30px;
  margin-bottom: 25px;
}

@media (max-width: 679px) {
  .app-root-container__paginator {
    margin-bottom: 25px;
  }

  .app-root-container__paginator_number {
    margin-top: 20px;
    margin-bottom: 20px;
  }
}
.app-root-container {
  display: flex;
  width: 100%;
  flex-grow: 1;
}

.app-root-container--simplified-iframe {
  min-height: 0;
}

.app-root-container--non-full-preview {
  height: 100vh;
}

.app-root {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  width: 100%;
  font-family: var(--fqz-poll-font-family);
  font-size: 14px;
  line-height: 1.2;
  background-color: #f5f6fa;
  color: var(--fqz-poll-text-on-bg);
  /* @TODO: Add polyfill for container queries */
  position: relative;
  display: flex;
  flex-direction: column;

  background-size: cover;
  background-position: center center;
  background-repeat: no-repeat;
  background-attachment: fixed;
  font-family: var(--fqz-poll-font-family);
  font-size: var(--fqz-poll-font-size);
  background-color: var(--fqz-poll-bg-color);
  background-image: var(--fqz-poll-finish-bg-image);
}

.app-root--simplified {
  color: var(--fqz-poll-text-on-place);
  display: flex;
  flex-direction: column;
  position: relative;
  background-image: var(--fqz-poll-simplified-bg-image, none);
  background-color: transparent;
}

.app-root--simplified-iframe {
  height: 100vh; /* fallback */
  height: 100dvh;
  min-height: var(--fqz-app-min-iframe-height, 0px);
}

.app-root--simplified-auto-height:not(.app-root--is-loading) {
  height: auto !important;
}

.app-root--simplified:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: var(--fqz-poll-main-place-color);
  pointer-events: none;
}

.app-root__covering {
  position: absolute;
  top: 0px;
  right: 0;
  bottom: 0;
  left: 0;
  background: #000000;
  opacity: 0.6;
  display: var(--fqz-poll-bg-cover);
}

.app-root--simplified .app-root__covering {
  display: none;
}

.app-root__info-screen-container {
  position: relative;
  z-index: 1;
  flex-grow: 1;
  margin: auto;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.app-root__foquz-banner {
  width: 100%;
  bottom: 0;
  z-index: 10;
}

.app-root__cover-banner {
  position: relative;
  z-index: 1;
}

.app-root__cover-banner--widget-topline-visible {
  position: relative;
  top: calc(var(--widget-topline-height-desktop) * -1);
}

.app-root--simplified .cover-banner {
  max-width: 100%;
  border-radius: 0;
  margin-top: 0;
  margin-bottom: -12px;
}

@media (max-width: 679px) {
  .app-root__cover-banner--widget-topline-visible {
    top: calc(var(--widget-topline-height-mobile) * -1);
  }

  .app-root--simplified .cover-banner {
    margin-bottom: 0;
  }
}

.poll-content--padding-top {
  padding-top: 35px;
}

/* Print report container */
.print-report-container {
  width: 100%;
  min-height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f5f6fa;
}

@media (max-width: 679px) {
  .app-root {
    --fqz-poll-finish-bg-image: var(--fqz-poll-mobile-bg-image) !important;
  }
  .poll-content--padding-top {
    padding-top: 25px;
  }
}

.app-root--print {
  background: white !important;
  color: black !important;
}

.app-root--print .app-root__covering {
  display: none !important;
}

.app-root--print .points-report-screen__footer {
  display: none !important;
}

@media print {
  .print-report-container {
    padding: 0 !important;
    margin: 0 !important;
    width: 100% !important;
    height: auto !important;
    background-color: white !important;
    -webkit-print-color-adjust: exact !important;
    print-color-adjust: exact !important;
  }

  /* Hide browser's default header/footer */
  @page {
    size: A4 portrait;
    margin: 0mm;
    padding: 0 !important;
  }

  /* Hide URL and title */
  @page :first {
    margin-top: 0;
  }
}

.poll-content {
  width: 100%;
}

.poll-content--simplified {
  padding-top: 0;
  display: flex;
  flex-grow: 1;
  flex-direction: column;
  min-height: 0;
  padding-bottom: 0;
}

.poll-content--simplified :deep(.poll-content__form),
.poll-content--simplified :deep(.poll-content__main) {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.app-root--simplified :deep(.poll-body__content) {
  justify-content: center;
}

.app-root--simplified-iframe :deep(.poll-body__content) {
  justify-content: flex-start;
}

.app-root--simplified :deep(.poll-body__content) {
  padding-top: 0;
}

.app-root--simplified .app-root__info-screen-container {
  margin-top: 0;
  margin-bottom: 0;
  padding-bottom: 50px;
}

.app-root--simplified:not(.app-root--simplified-iframe) .app-root__widget-topline {
  background: var(--fqz-poll-main-place-color);
}

.app-root--simplified:not(.app-root--simplified-iframe):has(.app-root__cover-banner)
  .app-root__widget-topline:not(.fqz-shadow-top--active) {
  background: transparent;
}

.app-root__poll-actions {
  position: relative;
}

.app-root--simplified .app-root__poll-actions::before {
  top: -15px;
}

@media (max-width: 679px) {
  .poll-content--simplified {
    padding-top: 0;
    padding-bottom: 0;
  }
  .app-root--simplified .app-root__info-screen-container {
    padding-bottom: 30px;
  }
}

@media (max-width: 360px) {
  .app-root .cover-banner {
    height: 76px;
    padding-top: 0;
  }
}

.app-root__foquz-label {
  display: none;
}
</style>
